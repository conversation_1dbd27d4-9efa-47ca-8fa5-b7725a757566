package repo

import (
	"context"
	"io"
	"strings"
	"sync"
	"time"
	"unicode/utf8"

	"git.woa.com/social-AIGC/aigc_access/config"
	"git.woa.com/social-AIGC/aigc_access/constant/errcode"
	faceIdStorage "git.woa.com/social-AIGC/aigc_access/qzone/face_id_storage"
	"git.woa.com/social-AIGC/aigc_access/stat"
	"golang.org/x/text/encoding/simplifiedchinese"
	"golang.org/x/text/transform"

	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/trpc-go/trpc-go/naming/registry"

	"git.woa.com/QzonePlatform/qzone-backend/common/photo/psa"
	qzpb "git.woa.com/trpcprotocol/qzone/storage_photo"
)

// QzoneProxy qzone代理
type QzoneProxy struct {
	qzoneConf     *config.QzoneConfig
	faceIDStorage *faceIdStorage.FaceIDStorage
}

// NewQzoneProxy 创建qzone代理
func NewQzoneProxy(qzoneConf *config.QzoneConfig) *QzoneProxy {
	return &QzoneProxy{
		qzoneConf:     qzoneConf,
		faceIDStorage: faceIdStorage.NewFaceIDStorage("d74f60b56c51d8f0d77203ae01a618a3"),
	}
}

// GetPhotoList 获取qzone相册列表
func (q *QzoneProxy) GetPhotoList(ctx context.Context, uin string) (photoList []*qzpb.PhotoInfo, err error) {
	st := stat.NewStat(ctx, stat.TypeCaller, "QzoneGetPhotoList")
	defer func() { st.Report(err, true) }()

	qzoneClient := psa.NewPSAClientProxy(q.qzoneConf.Bsid, uin)
	timeStart := time.Now()
	node := &registry.Node{}
	albumListType := uint32(2) // 带次索引
	reqAlbumList := &qzpb.AlbumListReq{
		Type: &albumListType,
	}
	//拉取相册列表
	rspAlbumList, err := qzoneClient.AlbumList(ctx, reqAlbumList, client.WithSelectorNode(node),
		client.WithCallerServiceName(q.qzoneConf.ServiceName))
	if err != nil {
		log.ErrorContextf(ctx, "qzone proxy AlbumList failed,  err=%v", err)
		if errs.Code(err) < 0 { // 尝试透传错误码
			return nil, err
		}
		return nil, errs.Wrap(err, errcode.ErrQzoneAlbumList, "")
	}
	log.Debugf("qzone proxy AlbumList cost=%d, uin=%s, count=%d",
		time.Since(timeStart).Milliseconds(), uin, len(rspAlbumList.AlbumInfos))

	timeStart = time.Now()
	var wg sync.WaitGroup                               // 用于等待所有goroutine完成
	sem := make(chan struct{}, q.qzoneConf.Concurrency) // 信号量，用于控制并发数
	var mu sync.Mutex                                   // 加锁，防止并发写
	rspList := make([][]*qzpb.PhotoListRsp, len(rspAlbumList.AlbumInfos))
	for i, album := range rspAlbumList.AlbumInfos {
		// 尝试GBK转UTF-8
		albumName, gbk2utf8 := tryGBK2UTF8(string(album.SecondaryInfo.Title))
		// 过滤指定相册，如果再在yaml的过滤中，则跳过
		if ok := q.qzoneConf.AlbumNameFilterMap[albumName]; ok {
			log.DebugContextf(ctx, "qzone proxy AlbumNameFileter, index=%d/%d, albumid=%s, total=%d, albumName=%s, gbk2utf8=%d",
				i, len(rspAlbumList.AlbumInfos), album.PrimaryInfo.Albumid, *album.PrimaryInfo.Total, albumName, gbk2utf8)
			continue
		}
		wg.Add(1)         // 增加等待计数
		sem <- struct{}{} // 获取信号量，如果已满则阻塞
		if err != nil {
			break
		}
		go func(albumInfo *qzpb.AlbumInfo, index int) {
			defer wg.Done()          // 完成时减少等待计数
			defer func() { <-sem }() // 释放信号量
			num := uint32(q.qzoneConf.BatchSize)
			start := uint32(1)
			albumId := albumInfo.PrimaryInfo.Albumid
			tryTimes := 0
			// 分批拉照片列表
			for {
				reqPhotoList := &qzpb.PhotoListReq{
					Albumid:  albumId,
					Start:    &start,
					Num:      &num,
					SortType: qzpb.PhotoSortType_UploadTimeDsc.Enum(), // 必须带排序，否则range无效，PhotoList返回所以照片
				}
				nodePhoto := &registry.Node{}
				rspPhotoList, errTemp := qzoneClient.PhotoList(ctx, reqPhotoList, client.WithSelectorNode(nodePhoto),
					client.WithCallerServiceName(q.qzoneConf.ServiceName))
				if errTemp != nil {
					log.ErrorContextf(ctx, "qzone proxy PhotoList failed,  index=%d/%d, albumid=%s, start=%d, tryTimes=%d, err=%v",
						index, len(rspAlbumList.AlbumInfos), albumId, start, tryTimes, errTemp)
					if tryTimes < q.qzoneConf.RetryTimes {
						// 失败重试
						tryTimes++
						continue
					}
					mu.Lock()
					if errs.Code(errTemp) < 0 { // 尝试透传错误码
						err = errTemp
					} else {
						err = errs.Wrap(errTemp, errcode.ErrQzonePhotoList, "")
					}
					st.AddWaterLog("callAddr", nodePhoto.Address)
					mu.Unlock()
					break
				}
				mu.Lock()
				rspList[index] = append(rspList[index], rspPhotoList)
				mu.Unlock()
				// log.Debugf("req PhotoList, selector=%v, req=%v", nodePhoto, reqPhotoList)
				log.Debugf("qzone proxy PhotoList index=%d/%d, albumid=%s, start=%d, num=%d, total=%d, count=%d",
					index, len(rspAlbumList.AlbumInfos), albumId, start, num, *rspPhotoList.TotalPhoto,
					len(rspPhotoList.PhotoInfos))
				start += uint32(len(rspPhotoList.PhotoInfos)) // 翻页，更新起始位置
				tryTimes = 0
				if start >= *rspPhotoList.TotalPhoto {
					break
				}
			}
		}(album, i)
	}
	wg.Wait() // 等待所有goroutine完成

	// log.InfoContextf(context.Background(), "\n PhotosList: %+v", rspList)

	// 汇总后一起处理，确保每次拉列表是有序的
	for _, albumRspList := range rspList {
		for _, rsp := range albumRspList {
			if rsp != nil {
				photoList = append(photoList, rsp.PhotoInfos...)
			}
		}
	}

	if err != nil {
		return nil, err
	}
	log.Debugf("qzone proxy PhotoList cost=%d, uin=%s, count=%d", time.Since(timeStart).Milliseconds(), uin, len(photoList))
	st.AddMetric(stat.MetricFileNum, len(photoList))
	return photoList, nil
}

func tryGBK2UTF8(s string) (string, int) {
	if utf8.ValidString(s) {
		return s, 0
	}
	reader := transform.NewReader(strings.NewReader(s), simplifiedchinese.GBK.NewDecoder())
	data, err := io.ReadAll(reader)
	if err != nil {
		return s, -1
	}
	return string(data), 1
}
