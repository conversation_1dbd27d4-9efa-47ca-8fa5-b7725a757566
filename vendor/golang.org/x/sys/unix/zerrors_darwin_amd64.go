// mkerrors.sh -m64
// Code generated by the command above; see README.md. DO NOT EDIT.

//go:build amd64 && darwin

// Code generated by cmd/cgo -godefs; DO NOT EDIT.
// cgo -godefs -- -m64 _const.go

package unix

import "syscall"

const (
	AF_APPLETALK                            = 0x10
	AF_CCITT                                = 0xa
	AF_CHAOS                                = 0x5
	AF_CNT                                  = 0x15
	AF_COIP                                 = 0x14
	AF_DATAKIT                              = 0x9
	AF_DECnet                               = 0xc
	AF_DLI                                  = 0xd
	AF_E164                                 = 0x1c
	AF_ECMA                                 = 0x8
	AF_HYLINK                               = 0xf
	AF_IEEE80211                            = 0x25
	AF_IMPLINK                              = 0x3
	AF_INET                                 = 0x2
	AF_INET6                                = 0x1e
	AF_IPX                                  = 0x17
	AF_ISDN                                 = 0x1c
	AF_ISO                                  = 0x7
	AF_LAT                                  = 0xe
	AF_LINK                                 = 0x12
	AF_LOCAL                                = 0x1
	AF_MAX                                  = 0x29
	AF_NATM                                 = 0x1f
	AF_NDRV                                 = 0x1b
	AF_NETBIOS                              = 0x21
	AF_NS                                   = 0x6
	AF_OSI                                  = 0x7
	AF_PPP                                  = 0x22
	AF_PUP                                  = 0x4
	AF_RESERVED_36                          = 0x24
	AF_ROUTE                                = 0x11
	AF_SIP                                  = 0x18
	AF_SNA                                  = 0xb
	AF_SYSTEM                               = 0x20
	AF_SYS_CONTROL                          = 0x2
	AF_UNIX                                 = 0x1
	AF_UNSPEC                               = 0x0
	AF_UTUN                                 = 0x26
	AF_VSOCK                                = 0x28
	ALTWERASE                               = 0x200
	ATTR_BIT_MAP_COUNT                      = 0x5
	ATTR_CMN_ACCESSMASK                     = 0x20000
	ATTR_CMN_ACCTIME                        = 0x1000
	ATTR_CMN_ADDEDTIME                      = 0x10000000
	ATTR_CMN_BKUPTIME                       = 0x2000
	ATTR_CMN_CHGTIME                        = 0x800
	ATTR_CMN_CRTIME                         = 0x200
	ATTR_CMN_DATA_PROTECT_FLAGS             = 0x40000000
	ATTR_CMN_DEVID                          = 0x2
	ATTR_CMN_DOCUMENT_ID                    = 0x100000
	ATTR_CMN_ERROR                          = 0x20000000
	ATTR_CMN_EXTENDED_SECURITY              = 0x400000
	ATTR_CMN_FILEID                         = 0x2000000
	ATTR_CMN_FLAGS                          = 0x40000
	ATTR_CMN_FNDRINFO                       = 0x4000
	ATTR_CMN_FSID                           = 0x4
	ATTR_CMN_FULLPATH                       = 0x8000000
	ATTR_CMN_GEN_COUNT                      = 0x80000
	ATTR_CMN_GRPID                          = 0x10000
	ATTR_CMN_GRPUUID                        = 0x1000000
	ATTR_CMN_MODTIME                        = 0x400
	ATTR_CMN_NAME                           = 0x1
	ATTR_CMN_NAMEDATTRCOUNT                 = 0x80000
	ATTR_CMN_NAMEDATTRLIST                  = 0x100000
	ATTR_CMN_OBJID                          = 0x20
	ATTR_CMN_OBJPERMANENTID                 = 0x40
	ATTR_CMN_OBJTAG                         = 0x10
	ATTR_CMN_OBJTYPE                        = 0x8
	ATTR_CMN_OWNERID                        = 0x8000
	ATTR_CMN_PARENTID                       = 0x4000000
	ATTR_CMN_PAROBJID                       = 0x80
	ATTR_CMN_RETURNED_ATTRS                 = 0x80000000
	ATTR_CMN_SCRIPT                         = 0x100
	ATTR_CMN_SETMASK                        = 0x51c7ff00
	ATTR_CMN_USERACCESS                     = 0x200000
	ATTR_CMN_UUID                           = 0x800000
	ATTR_CMN_VALIDMASK                      = 0xffffffff
	ATTR_CMN_VOLSETMASK                     = 0x6700
	ATTR_FILE_ALLOCSIZE                     = 0x4
	ATTR_FILE_CLUMPSIZE                     = 0x10
	ATTR_FILE_DATAALLOCSIZE                 = 0x400
	ATTR_FILE_DATAEXTENTS                   = 0x800
	ATTR_FILE_DATALENGTH                    = 0x200
	ATTR_FILE_DEVTYPE                       = 0x20
	ATTR_FILE_FILETYPE                      = 0x40
	ATTR_FILE_FORKCOUNT                     = 0x80
	ATTR_FILE_FORKLIST                      = 0x100
	ATTR_FILE_IOBLOCKSIZE                   = 0x8
	ATTR_FILE_LINKCOUNT                     = 0x1
	ATTR_FILE_RSRCALLOCSIZE                 = 0x2000
	ATTR_FILE_RSRCEXTENTS                   = 0x4000
	ATTR_FILE_RSRCLENGTH                    = 0x1000
	ATTR_FILE_SETMASK                       = 0x20
	ATTR_FILE_TOTALSIZE                     = 0x2
	ATTR_FILE_VALIDMASK                     = 0x37ff
	ATTR_VOL_ALLOCATIONCLUMP                = 0x40
	ATTR_VOL_ATTRIBUTES                     = 0x40000000
	ATTR_VOL_CAPABILITIES                   = 0x20000
	ATTR_VOL_DIRCOUNT                       = 0x400
	ATTR_VOL_ENCODINGSUSED                  = 0x10000
	ATTR_VOL_FILECOUNT                      = 0x200
	ATTR_VOL_FSTYPE                         = 0x1
	ATTR_VOL_INFO                           = 0x80000000
	ATTR_VOL_IOBLOCKSIZE                    = 0x80
	ATTR_VOL_MAXOBJCOUNT                    = 0x800
	ATTR_VOL_MINALLOCATION                  = 0x20
	ATTR_VOL_MOUNTEDDEVICE                  = 0x8000
	ATTR_VOL_MOUNTFLAGS                     = 0x4000
	ATTR_VOL_MOUNTPOINT                     = 0x1000
	ATTR_VOL_NAME                           = 0x2000
	ATTR_VOL_OBJCOUNT                       = 0x100
	ATTR_VOL_QUOTA_SIZE                     = 0x10000000
	ATTR_VOL_RESERVED_SIZE                  = 0x20000000
	ATTR_VOL_SETMASK                        = 0x80002000
	ATTR_VOL_SIGNATURE                      = 0x2
	ATTR_VOL_SIZE                           = 0x4
	ATTR_VOL_SPACEAVAIL                     = 0x10
	ATTR_VOL_SPACEFREE                      = 0x8
	ATTR_VOL_SPACEUSED                      = 0x800000
	ATTR_VOL_UUID                           = 0x40000
	ATTR_VOL_VALIDMASK                      = 0xf087ffff
	B0                                      = 0x0
	B110                                    = 0x6e
	B115200                                 = 0x1c200
	B1200                                   = 0x4b0
	B134                                    = 0x86
	B14400                                  = 0x3840
	B150                                    = 0x96
	B1800                                   = 0x708
	B19200                                  = 0x4b00
	B200                                    = 0xc8
	B230400                                 = 0x38400
	B2400                                   = 0x960
	B28800                                  = 0x7080
	B300                                    = 0x12c
	B38400                                  = 0x9600
	B4800                                   = 0x12c0
	B50                                     = 0x32
	B57600                                  = 0xe100
	B600                                    = 0x258
	B7200                                   = 0x1c20
	B75                                     = 0x4b
	B76800                                  = 0x12c00
	B9600                                   = 0x2580
	BIOCFLUSH                               = 0x20004268
	BIOCGBLEN                               = 0x40044266
	BIOCGDLT                                = 0x4004426a
	BIOCGDLTLIST                            = 0xc00c4279
	BIOCGETIF                               = 0x4020426b
	BIOCGHDRCMPLT                           = 0x40044274
	BIOCGRSIG                               = 0x40044272
	BIOCGRTIMEOUT                           = 0x4010426e
	BIOCGSEESENT                            = 0x40044276
	BIOCGSTATS                              = 0x4008426f
	BIOCIMMEDIATE                           = 0x80044270
	BIOCPROMISC                             = 0x20004269
	BIOCSBLEN                               = 0xc0044266
	BIOCSDLT                                = 0x80044278
	BIOCSETF                                = 0x80104267
	BIOCSETFNR                              = 0x8010427e
	BIOCSETIF                               = 0x8020426c
	BIOCSHDRCMPLT                           = 0x80044275
	BIOCSRSIG                               = 0x80044273
	BIOCSRTIMEOUT                           = 0x8010426d
	BIOCSSEESENT                            = 0x80044277
	BIOCVERSION                             = 0x40044271
	BPF_A                                   = 0x10
	BPF_ABS                                 = 0x20
	BPF_ADD                                 = 0x0
	BPF_ALIGNMENT                           = 0x4
	BPF_ALU                                 = 0x4
	BPF_AND                                 = 0x50
	BPF_B                                   = 0x10
	BPF_DIV                                 = 0x30
	BPF_H                                   = 0x8
	BPF_IMM                                 = 0x0
	BPF_IND                                 = 0x40
	BPF_JA                                  = 0x0
	BPF_JEQ                                 = 0x10
	BPF_JGE                                 = 0x30
	BPF_JGT                                 = 0x20
	BPF_JMP                                 = 0x5
	BPF_JSET                                = 0x40
	BPF_K                                   = 0x0
	BPF_LD                                  = 0x0
	BPF_LDX                                 = 0x1
	BPF_LEN                                 = 0x80
	BPF_LSH                                 = 0x60
	BPF_MAJOR_VERSION                       = 0x1
	BPF_MAXBUFSIZE                          = 0x80000
	BPF_MAXINSNS                            = 0x200
	BPF_MEM                                 = 0x60
	BPF_MEMWORDS                            = 0x10
	BPF_MINBUFSIZE                          = 0x20
	BPF_MINOR_VERSION                       = 0x1
	BPF_MISC                                = 0x7
	BPF_MSH                                 = 0xa0
	BPF_MUL                                 = 0x20
	BPF_NEG                                 = 0x80
	BPF_OR                                  = 0x40
	BPF_RELEASE                             = 0x30bb6
	BPF_RET                                 = 0x6
	BPF_RSH                                 = 0x70
	BPF_ST                                  = 0x2
	BPF_STX                                 = 0x3
	BPF_SUB                                 = 0x10
	BPF_TAX                                 = 0x0
	BPF_TXA                                 = 0x80
	BPF_W                                   = 0x0
	BPF_X                                   = 0x8
	BRKINT                                  = 0x2
	BS0                                     = 0x0
	BS1                                     = 0x8000
	BSDLY                                   = 0x8000
	CFLUSH                                  = 0xf
	CLOCAL                                  = 0x8000
	CLOCK_MONOTONIC                         = 0x6
	CLOCK_MONOTONIC_RAW                     = 0x4
	CLOCK_MONOTONIC_RAW_APPROX              = 0x5
	CLOCK_PROCESS_CPUTIME_ID                = 0xc
	CLOCK_REALTIME                          = 0x0
	CLOCK_THREAD_CPUTIME_ID                 = 0x10
	CLOCK_UPTIME_RAW                        = 0x8
	CLOCK_UPTIME_RAW_APPROX                 = 0x9
	CLONE_NOFOLLOW                          = 0x1
	CLONE_NOOWNERCOPY                       = 0x2
	CONNECT_DATA_AUTHENTICATED              = 0x4
	CONNECT_DATA_IDEMPOTENT                 = 0x2
	CONNECT_RESUME_ON_READ_WRITE            = 0x1
	CR0                                     = 0x0
	CR1                                     = 0x1000
	CR2                                     = 0x2000
	CR3                                     = 0x3000
	CRDLY                                   = 0x3000
	CREAD                                   = 0x800
	CRTSCTS                                 = 0x30000
	CS5                                     = 0x0
	CS6                                     = 0x100
	CS7                                     = 0x200
	CS8                                     = 0x300
	CSIZE                                   = 0x300
	CSTART                                  = 0x11
	CSTATUS                                 = 0x14
	CSTOP                                   = 0x13
	CSTOPB                                  = 0x400
	CSUSP                                   = 0x1a
	CTLIOCGINFO                             = 0xc0644e03
	CTL_HW                                  = 0x6
	CTL_KERN                                = 0x1
	CTL_MAXNAME                             = 0xc
	CTL_NET                                 = 0x4
	DLT_A429                                = 0xb8
	DLT_A653_ICM                            = 0xb9
	DLT_AIRONET_HEADER                      = 0x78
	DLT_AOS                                 = 0xde
	DLT_APPLE_IP_OVER_IEEE1394              = 0x8a
	DLT_ARCNET                              = 0x7
	DLT_ARCNET_LINUX                        = 0x81
	DLT_ATM_CLIP                            = 0x13
	DLT_ATM_RFC1483                         = 0xb
	DLT_AURORA                              = 0x7e
	DLT_AX25                                = 0x3
	DLT_AX25_KISS                           = 0xca
	DLT_BACNET_MS_TP                        = 0xa5
	DLT_BLUETOOTH_HCI_H4                    = 0xbb
	DLT_BLUETOOTH_HCI_H4_WITH_PHDR          = 0xc9
	DLT_CAN20B                              = 0xbe
	DLT_CAN_SOCKETCAN                       = 0xe3
	DLT_CHAOS                               = 0x5
	DLT_CHDLC                               = 0x68
	DLT_CISCO_IOS                           = 0x76
	DLT_C_HDLC                              = 0x68
	DLT_C_HDLC_WITH_DIR                     = 0xcd
	DLT_DBUS                                = 0xe7
	DLT_DECT                                = 0xdd
	DLT_DOCSIS                              = 0x8f
	DLT_DVB_CI                              = 0xeb
	DLT_ECONET                              = 0x73
	DLT_EN10MB                              = 0x1
	DLT_EN3MB                               = 0x2
	DLT_ENC                                 = 0x6d
	DLT_ERF                                 = 0xc5
	DLT_ERF_ETH                             = 0xaf
	DLT_ERF_POS                             = 0xb0
	DLT_FC_2                                = 0xe0
	DLT_FC_2_WITH_FRAME_DELIMS              = 0xe1
	DLT_FDDI                                = 0xa
	DLT_FLEXRAY                             = 0xd2
	DLT_FRELAY                              = 0x6b
	DLT_FRELAY_WITH_DIR                     = 0xce
	DLT_GCOM_SERIAL                         = 0xad
	DLT_GCOM_T1E1                           = 0xac
	DLT_GPF_F                               = 0xab
	DLT_GPF_T                               = 0xaa
	DLT_GPRS_LLC                            = 0xa9
	DLT_GSMTAP_ABIS                         = 0xda
	DLT_GSMTAP_UM                           = 0xd9
	DLT_HHDLC                               = 0x79
	DLT_IBM_SN                              = 0x92
	DLT_IBM_SP                              = 0x91
	DLT_IEEE802                             = 0x6
	DLT_IEEE802_11                          = 0x69
	DLT_IEEE802_11_RADIO                    = 0x7f
	DLT_IEEE802_11_RADIO_AVS                = 0xa3
	DLT_IEEE802_15_4                        = 0xc3
	DLT_IEEE802_15_4_LINUX                  = 0xbf
	DLT_IEEE802_15_4_NOFCS                  = 0xe6
	DLT_IEEE802_15_4_NONASK_PHY             = 0xd7
	DLT_IEEE802_16_MAC_CPS                  = 0xbc
	DLT_IEEE802_16_MAC_CPS_RADIO            = 0xc1
	DLT_IPFILTER                            = 0x74
	DLT_IPMB                                = 0xc7
	DLT_IPMB_LINUX                          = 0xd1
	DLT_IPNET                               = 0xe2
	DLT_IPOIB                               = 0xf2
	DLT_IPV4                                = 0xe4
	DLT_IPV6                                = 0xe5
	DLT_IP_OVER_FC                          = 0x7a
	DLT_JUNIPER_ATM1                        = 0x89
	DLT_JUNIPER_ATM2                        = 0x87
	DLT_JUNIPER_ATM_CEMIC                   = 0xee
	DLT_JUNIPER_CHDLC                       = 0xb5
	DLT_JUNIPER_ES                          = 0x84
	DLT_JUNIPER_ETHER                       = 0xb2
	DLT_JUNIPER_FIBRECHANNEL                = 0xea
	DLT_JUNIPER_FRELAY                      = 0xb4
	DLT_JUNIPER_GGSN                        = 0x85
	DLT_JUNIPER_ISM                         = 0xc2
	DLT_JUNIPER_MFR                         = 0x86
	DLT_JUNIPER_MLFR                        = 0x83
	DLT_JUNIPER_MLPPP                       = 0x82
	DLT_JUNIPER_MONITOR                     = 0xa4
	DLT_JUNIPER_PIC_PEER                    = 0xae
	DLT_JUNIPER_PPP                         = 0xb3
	DLT_JUNIPER_PPPOE                       = 0xa7
	DLT_JUNIPER_PPPOE_ATM                   = 0xa8
	DLT_JUNIPER_SERVICES                    = 0x88
	DLT_JUNIPER_SRX_E2E                     = 0xe9
	DLT_JUNIPER_ST                          = 0xc8
	DLT_JUNIPER_VP                          = 0xb7
	DLT_JUNIPER_VS                          = 0xe8
	DLT_LAPB_WITH_DIR                       = 0xcf
	DLT_LAPD                                = 0xcb
	DLT_LIN                                 = 0xd4
	DLT_LINUX_EVDEV                         = 0xd8
	DLT_LINUX_IRDA                          = 0x90
	DLT_LINUX_LAPD                          = 0xb1
	DLT_LINUX_PPP_WITHDIRECTION             = 0xa6
	DLT_LINUX_SLL                           = 0x71
	DLT_LOOP                                = 0x6c
	DLT_LTALK                               = 0x72
	DLT_MATCHING_MAX                        = 0x10a
	DLT_MATCHING_MIN                        = 0x68
	DLT_MFR                                 = 0xb6
	DLT_MOST                                = 0xd3
	DLT_MPEG_2_TS                           = 0xf3
	DLT_MPLS                                = 0xdb
	DLT_MTP2                                = 0x8c
	DLT_MTP2_WITH_PHDR                      = 0x8b
	DLT_MTP3                                = 0x8d
	DLT_MUX27010                            = 0xec
	DLT_NETANALYZER                         = 0xf0
	DLT_NETANALYZER_TRANSPARENT             = 0xf1
	DLT_NFC_LLCP                            = 0xf5
	DLT_NFLOG                               = 0xef
	DLT_NG40                                = 0xf4
	DLT_NULL                                = 0x0
	DLT_PCI_EXP                             = 0x7d
	DLT_PFLOG                               = 0x75
	DLT_PFSYNC                              = 0x12
	DLT_PPI                                 = 0xc0
	DLT_PPP                                 = 0x9
	DLT_PPP_BSDOS                           = 0x10
	DLT_PPP_ETHER                           = 0x33
	DLT_PPP_PPPD                            = 0xa6
	DLT_PPP_SERIAL                          = 0x32
	DLT_PPP_WITH_DIR                        = 0xcc
	DLT_PPP_WITH_DIRECTION                  = 0xa6
	DLT_PRISM_HEADER                        = 0x77
	DLT_PRONET                              = 0x4
	DLT_RAIF1                               = 0xc6
	DLT_RAW                                 = 0xc
	DLT_RIO                                 = 0x7c
	DLT_SCCP                                = 0x8e
	DLT_SITA                                = 0xc4
	DLT_SLIP                                = 0x8
	DLT_SLIP_BSDOS                          = 0xf
	DLT_STANAG_5066_D_PDU                   = 0xed
	DLT_SUNATM                              = 0x7b
	DLT_SYMANTEC_FIREWALL                   = 0x63
	DLT_TZSP                                = 0x80
	DLT_USB                                 = 0xba
	DLT_USB_DARWIN                          = 0x10a
	DLT_USB_LINUX                           = 0xbd
	DLT_USB_LINUX_MMAPPED                   = 0xdc
	DLT_USER0                               = 0x93
	DLT_USER1                               = 0x94
	DLT_USER10                              = 0x9d
	DLT_USER11                              = 0x9e
	DLT_USER12                              = 0x9f
	DLT_USER13                              = 0xa0
	DLT_USER14                              = 0xa1
	DLT_USER15                              = 0xa2
	DLT_USER2                               = 0x95
	DLT_USER3                               = 0x96
	DLT_USER4                               = 0x97
	DLT_USER5                               = 0x98
	DLT_USER6                               = 0x99
	DLT_USER7                               = 0x9a
	DLT_USER8                               = 0x9b
	DLT_USER9                               = 0x9c
	DLT_WIHART                              = 0xdf
	DLT_X2E_SERIAL                          = 0xd5
	DLT_X2E_XORAYA                          = 0xd6
	DT_BLK                                  = 0x6
	DT_CHR                                  = 0x2
	DT_DIR                                  = 0x4
	DT_FIFO                                 = 0x1
	DT_LNK                                  = 0xa
	DT_REG                                  = 0x8
	DT_SOCK                                 = 0xc
	DT_UNKNOWN                              = 0x0
	DT_WHT                                  = 0xe
	ECHO                                    = 0x8
	ECHOCTL                                 = 0x40
	ECHOE                                   = 0x2
	ECHOK                                   = 0x4
	ECHOKE                                  = 0x1
	ECHONL                                  = 0x10
	ECHOPRT                                 = 0x20
	EVFILT_AIO                              = -0x3
	EVFILT_EXCEPT                           = -0xf
	EVFILT_FS                               = -0x9
	EVFILT_MACHPORT                         = -0x8
	EVFILT_PROC                             = -0x5
	EVFILT_READ                             = -0x1
	EVFILT_SIGNAL                           = -0x6
	EVFILT_SYSCOUNT                         = 0x11
	EVFILT_THREADMARKER                     = 0x11
	EVFILT_TIMER                            = -0x7
	EVFILT_USER                             = -0xa
	EVFILT_VM                               = -0xc
	EVFILT_VNODE                            = -0x4
	EVFILT_WRITE                            = -0x2
	EV_ADD                                  = 0x1
	EV_CLEAR                                = 0x20
	EV_DELETE                               = 0x2
	EV_DISABLE                              = 0x8
	EV_DISPATCH                             = 0x80
	EV_DISPATCH2                            = 0x180
	EV_ENABLE                               = 0x4
	EV_EOF                                  = 0x8000
	EV_ERROR                                = 0x4000
	EV_FLAG0                                = 0x1000
	EV_FLAG1                                = 0x2000
	EV_ONESHOT                              = 0x10
	EV_OOBAND                               = 0x2000
	EV_POLL                                 = 0x1000
	EV_RECEIPT                              = 0x40
	EV_SYSFLAGS                             = 0xf000
	EV_UDATA_SPECIFIC                       = 0x100
	EV_VANISHED                             = 0x200
	EXTA                                    = 0x4b00
	EXTB                                    = 0x9600
	EXTPROC                                 = 0x800
	FD_CLOEXEC                              = 0x1
	FD_SETSIZE                              = 0x400
	FF0                                     = 0x0
	FF1                                     = 0x4000
	FFDLY                                   = 0x4000
	FLUSHO                                  = 0x800000
	FSOPT_ATTR_CMN_EXTENDED                 = 0x20
	FSOPT_NOFOLLOW                          = 0x1
	FSOPT_NOINMEMUPDATE                     = 0x2
	FSOPT_PACK_INVAL_ATTRS                  = 0x8
	FSOPT_REPORT_FULLSIZE                   = 0x4
	FSOPT_RETURN_REALDEV                    = 0x200
	F_ADDFILESIGS                           = 0x3d
	F_ADDFILESIGS_FOR_DYLD_SIM              = 0x53
	F_ADDFILESIGS_INFO                      = 0x67
	F_ADDFILESIGS_RETURN                    = 0x61
	F_ADDFILESUPPL                          = 0x68
	F_ADDSIGS                               = 0x3b
	F_ALLOCATEALL                           = 0x4
	F_ALLOCATECONTIG                        = 0x2
	F_BARRIERFSYNC                          = 0x55
	F_CHECK_LV                              = 0x62
	F_CHKCLEAN                              = 0x29
	F_DUPFD                                 = 0x0
	F_DUPFD_CLOEXEC                         = 0x43
	F_FINDSIGS                              = 0x4e
	F_FLUSH_DATA                            = 0x28
	F_FREEZE_FS                             = 0x35
	F_FULLFSYNC                             = 0x33
	F_GETCODEDIR                            = 0x48
	F_GETFD                                 = 0x1
	F_GETFL                                 = 0x3
	F_GETLK                                 = 0x7
	F_GETLKPID                              = 0x42
	F_GETNOSIGPIPE                          = 0x4a
	F_GETOWN                                = 0x5
	F_GETPATH                               = 0x32
	F_GETPATH_MTMINFO                       = 0x47
	F_GETPATH_NOFIRMLINK                    = 0x66
	F_GETPROTECTIONCLASS                    = 0x3f
	F_GETPROTECTIONLEVEL                    = 0x4d
	F_GETSIGSINFO                           = 0x69
	F_GLOBAL_NOCACHE                        = 0x37
	F_LOG2PHYS                              = 0x31
	F_LOG2PHYS_EXT                          = 0x41
	F_NOCACHE                               = 0x30
	F_NODIRECT                              = 0x3e
	F_OK                                    = 0x0
	F_PATHPKG_CHECK                         = 0x34
	F_PEOFPOSMODE                           = 0x3
	F_PREALLOCATE                           = 0x2a
	F_PUNCHHOLE                             = 0x63
	F_RDADVISE                              = 0x2c
	F_RDAHEAD                               = 0x2d
	F_RDLCK                                 = 0x1
	F_SETBACKINGSTORE                       = 0x46
	F_SETFD                                 = 0x2
	F_SETFL                                 = 0x4
	F_SETLK                                 = 0x8
	F_SETLKW                                = 0x9
	F_SETLKWTIMEOUT                         = 0xa
	F_SETNOSIGPIPE                          = 0x49
	F_SETOWN                                = 0x6
	F_SETPROTECTIONCLASS                    = 0x40
	F_SETSIZE                               = 0x2b
	F_SINGLE_WRITER                         = 0x4c
	F_SPECULATIVE_READ                      = 0x65
	F_THAW_FS                               = 0x36
	F_TRANSCODEKEY                          = 0x4b
	F_TRIM_ACTIVE_FILE                      = 0x64
	F_UNLCK                                 = 0x2
	F_VOLPOSMODE                            = 0x4
	F_WRLCK                                 = 0x3
	HUPCL                                   = 0x4000
	HW_MACHINE                              = 0x1
	ICANON                                  = 0x100
	ICMP6_FILTER                            = 0x12
	ICRNL                                   = 0x100
	IEXTEN                                  = 0x400
	IFF_ALLMULTI                            = 0x200
	IFF_ALTPHYS                             = 0x4000
	IFF_BROADCAST                           = 0x2
	IFF_DEBUG                               = 0x4
	IFF_LINK0                               = 0x1000
	IFF_LINK1                               = 0x2000
	IFF_LINK2                               = 0x4000
	IFF_LOOPBACK                            = 0x8
	IFF_MULTICAST                           = 0x8000
	IFF_NOARP                               = 0x80
	IFF_NOTRAILERS                          = 0x20
	IFF_OACTIVE                             = 0x400
	IFF_POINTOPOINT                         = 0x10
	IFF_PROMISC                             = 0x100
	IFF_RUNNING                             = 0x40
	IFF_SIMPLEX                             = 0x800
	IFF_UP                                  = 0x1
	IFNAMSIZ                                = 0x10
	IFT_1822                                = 0x2
	IFT_6LOWPAN                             = 0x40
	IFT_AAL5                                = 0x31
	IFT_ARCNET                              = 0x23
	IFT_ARCNETPLUS                          = 0x24
	IFT_ATM                                 = 0x25
	IFT_BRIDGE                              = 0xd1
	IFT_CARP                                = 0xf8
	IFT_CELLULAR                            = 0xff
	IFT_CEPT                                = 0x13
	IFT_DS3                                 = 0x1e
	IFT_ENC                                 = 0xf4
	IFT_EON                                 = 0x19
	IFT_ETHER                               = 0x6
	IFT_FAITH                               = 0x38
	IFT_FDDI                                = 0xf
	IFT_FRELAY                              = 0x20
	IFT_FRELAYDCE                           = 0x2c
	IFT_GIF                                 = 0x37
	IFT_HDH1822                             = 0x3
	IFT_HIPPI                               = 0x2f
	IFT_HSSI                                = 0x2e
	IFT_HY                                  = 0xe
	IFT_IEEE1394                            = 0x90
	IFT_IEEE8023ADLAG                       = 0x88
	IFT_ISDNBASIC                           = 0x14
	IFT_ISDNPRIMARY                         = 0x15
	IFT_ISO88022LLC                         = 0x29
	IFT_ISO88023                            = 0x7
	IFT_ISO88024                            = 0x8
	IFT_ISO88025                            = 0x9
	IFT_ISO88026                            = 0xa
	IFT_L2VLAN                              = 0x87
	IFT_LAPB                                = 0x10
	IFT_LOCALTALK                           = 0x2a
	IFT_LOOP                                = 0x18
	IFT_MIOX25                              = 0x26
	IFT_MODEM                               = 0x30
	IFT_NSIP                                = 0x1b
	IFT_OTHER                               = 0x1
	IFT_P10                                 = 0xc
	IFT_P80                                 = 0xd
	IFT_PARA                                = 0x22
	IFT_PDP                                 = 0xff
	IFT_PFLOG                               = 0xf5
	IFT_PFSYNC                              = 0xf6
	IFT_PKTAP                               = 0xfe
	IFT_PPP                                 = 0x17
	IFT_PROPMUX                             = 0x36
	IFT_PROPVIRTUAL                         = 0x35
	IFT_PTPSERIAL                           = 0x16
	IFT_RS232                               = 0x21
	IFT_SDLC                                = 0x11
	IFT_SIP                                 = 0x1f
	IFT_SLIP                                = 0x1c
	IFT_SMDSDXI                             = 0x2b
	IFT_SMDSICIP                            = 0x34
	IFT_SONET                               = 0x27
	IFT_SONETPATH                           = 0x32
	IFT_SONETVT                             = 0x33
	IFT_STARLAN                             = 0xb
	IFT_STF                                 = 0x39
	IFT_T1                                  = 0x12
	IFT_ULTRA                               = 0x1d
	IFT_V35                                 = 0x2d
	IFT_X25                                 = 0x5
	IFT_X25DDN                              = 0x4
	IFT_X25PLE                              = 0x28
	IFT_XETHER                              = 0x1a
	IGNBRK                                  = 0x1
	IGNCR                                   = 0x80
	IGNPAR                                  = 0x4
	IMAXBEL                                 = 0x2000
	INLCR                                   = 0x40
	INPCK                                   = 0x10
	IN_CLASSA_HOST                          = 0xffffff
	IN_CLASSA_MAX                           = 0x80
	IN_CLASSA_NET                           = 0xff000000
	IN_CLASSA_NSHIFT                        = 0x18
	IN_CLASSB_HOST                          = 0xffff
	IN_CLASSB_MAX                           = 0x10000
	IN_CLASSB_NET                           = 0xffff0000
	IN_CLASSB_NSHIFT                        = 0x10
	IN_CLASSC_HOST                          = 0xff
	IN_CLASSC_NET                           = 0xffffff00
	IN_CLASSC_NSHIFT                        = 0x8
	IN_CLASSD_HOST                          = 0xfffffff
	IN_CLASSD_NET                           = 0xf0000000
	IN_CLASSD_NSHIFT                        = 0x1c
	IN_LINKLOCALNETNUM                      = 0xa9fe0000
	IN_LOOPBACKNET                          = 0x7f
	IOCTL_VM_SOCKETS_GET_LOCAL_CID          = 0x400473d1
	IPPROTO_3PC                             = 0x22
	IPPROTO_ADFS                            = 0x44
	IPPROTO_AH                              = 0x33
	IPPROTO_AHIP                            = 0x3d
	IPPROTO_APES                            = 0x63
	IPPROTO_ARGUS                           = 0xd
	IPPROTO_AX25                            = 0x5d
	IPPROTO_BHA                             = 0x31
	IPPROTO_BLT                             = 0x1e
	IPPROTO_BRSATMON                        = 0x4c
	IPPROTO_CFTP                            = 0x3e
	IPPROTO_CHAOS                           = 0x10
	IPPROTO_CMTP                            = 0x26
	IPPROTO_CPHB                            = 0x49
	IPPROTO_CPNX                            = 0x48
	IPPROTO_DDP                             = 0x25
	IPPROTO_DGP                             = 0x56
	IPPROTO_DIVERT                          = 0xfe
	IPPROTO_DONE                            = 0x101
	IPPROTO_DSTOPTS                         = 0x3c
	IPPROTO_EGP                             = 0x8
	IPPROTO_EMCON                           = 0xe
	IPPROTO_ENCAP                           = 0x62
	IPPROTO_EON                             = 0x50
	IPPROTO_ESP                             = 0x32
	IPPROTO_ETHERIP                         = 0x61
	IPPROTO_FRAGMENT                        = 0x2c
	IPPROTO_GGP                             = 0x3
	IPPROTO_GMTP                            = 0x64
	IPPROTO_GRE                             = 0x2f
	IPPROTO_HELLO                           = 0x3f
	IPPROTO_HMP                             = 0x14
	IPPROTO_HOPOPTS                         = 0x0
	IPPROTO_ICMP                            = 0x1
	IPPROTO_ICMPV6                          = 0x3a
	IPPROTO_IDP                             = 0x16
	IPPROTO_IDPR                            = 0x23
	IPPROTO_IDRP                            = 0x2d
	IPPROTO_IGMP                            = 0x2
	IPPROTO_IGP                             = 0x55
	IPPROTO_IGRP                            = 0x58
	IPPROTO_IL                              = 0x28
	IPPROTO_INLSP                           = 0x34
	IPPROTO_INP                             = 0x20
	IPPROTO_IP                              = 0x0
	IPPROTO_IPCOMP                          = 0x6c
	IPPROTO_IPCV                            = 0x47
	IPPROTO_IPEIP                           = 0x5e
	IPPROTO_IPIP                            = 0x4
	IPPROTO_IPPC                            = 0x43
	IPPROTO_IPV4                            = 0x4
	IPPROTO_IPV6                            = 0x29
	IPPROTO_IRTP                            = 0x1c
	IPPROTO_KRYPTOLAN                       = 0x41
	IPPROTO_LARP                            = 0x5b
	IPPROTO_LEAF1                           = 0x19
	IPPROTO_LEAF2                           = 0x1a
	IPPROTO_MAX                             = 0x100
	IPPROTO_MAXID                           = 0x34
	IPPROTO_MEAS                            = 0x13
	IPPROTO_MHRP                            = 0x30
	IPPROTO_MICP                            = 0x5f
	IPPROTO_MTP                             = 0x5c
	IPPROTO_MUX                             = 0x12
	IPPROTO_ND                              = 0x4d
	IPPROTO_NHRP                            = 0x36
	IPPROTO_NONE                            = 0x3b
	IPPROTO_NSP                             = 0x1f
	IPPROTO_NVPII                           = 0xb
	IPPROTO_OSPFIGP                         = 0x59
	IPPROTO_PGM                             = 0x71
	IPPROTO_PIGP                            = 0x9
	IPPROTO_PIM                             = 0x67
	IPPROTO_PRM                             = 0x15
	IPPROTO_PUP                             = 0xc
	IPPROTO_PVP                             = 0x4b
	IPPROTO_RAW                             = 0xff
	IPPROTO_RCCMON                          = 0xa
	IPPROTO_RDP                             = 0x1b
	IPPROTO_ROUTING                         = 0x2b
	IPPROTO_RSVP                            = 0x2e
	IPPROTO_RVD                             = 0x42
	IPPROTO_SATEXPAK                        = 0x40
	IPPROTO_SATMON                          = 0x45
	IPPROTO_SCCSP                           = 0x60
	IPPROTO_SCTP                            = 0x84
	IPPROTO_SDRP                            = 0x2a
	IPPROTO_SEP                             = 0x21
	IPPROTO_SRPC                            = 0x5a
	IPPROTO_ST                              = 0x7
	IPPROTO_SVMTP                           = 0x52
	IPPROTO_SWIPE                           = 0x35
	IPPROTO_TCF                             = 0x57
	IPPROTO_TCP                             = 0x6
	IPPROTO_TP                              = 0x1d
	IPPROTO_TPXX                            = 0x27
	IPPROTO_TRUNK1                          = 0x17
	IPPROTO_TRUNK2                          = 0x18
	IPPROTO_TTP                             = 0x54
	IPPROTO_UDP                             = 0x11
	IPPROTO_VINES                           = 0x53
	IPPROTO_VISA                            = 0x46
	IPPROTO_VMTP                            = 0x51
	IPPROTO_WBEXPAK                         = 0x4f
	IPPROTO_WBMON                           = 0x4e
	IPPROTO_WSN                             = 0x4a
	IPPROTO_XNET                            = 0xf
	IPPROTO_XTP                             = 0x24
	IPV6_2292DSTOPTS                        = 0x17
	IPV6_2292HOPLIMIT                       = 0x14
	IPV6_2292HOPOPTS                        = 0x16
	IPV6_2292NEXTHOP                        = 0x15
	IPV6_2292PKTINFO                        = 0x13
	IPV6_2292PKTOPTIONS                     = 0x19
	IPV6_2292RTHDR                          = 0x18
	IPV6_3542DSTOPTS                        = 0x32
	IPV6_3542HOPLIMIT                       = 0x2f
	IPV6_3542HOPOPTS                        = 0x31
	IPV6_3542NEXTHOP                        = 0x30
	IPV6_3542PKTINFO                        = 0x2e
	IPV6_3542RTHDR                          = 0x33
	IPV6_ADDR_MC_FLAGS_PREFIX               = 0x20
	IPV6_ADDR_MC_FLAGS_TRANSIENT            = 0x10
	IPV6_ADDR_MC_FLAGS_UNICAST_BASED        = 0x30
	IPV6_AUTOFLOWLABEL                      = 0x3b
	IPV6_BINDV6ONLY                         = 0x1b
	IPV6_BOUND_IF                           = 0x7d
	IPV6_CHECKSUM                           = 0x1a
	IPV6_DEFAULT_MULTICAST_HOPS             = 0x1
	IPV6_DEFAULT_MULTICAST_LOOP             = 0x1
	IPV6_DEFHLIM                            = 0x40
	IPV6_DONTFRAG                           = 0x3e
	IPV6_DSTOPTS                            = 0x32
	IPV6_FAITH                              = 0x1d
	IPV6_FLOWINFO_MASK                      = 0xffffff0f
	IPV6_FLOWLABEL_MASK                     = 0xffff0f00
	IPV6_FLOW_ECN_MASK                      = 0x3000
	IPV6_FRAGTTL                            = 0x3c
	IPV6_FW_ADD                             = 0x1e
	IPV6_FW_DEL                             = 0x1f
	IPV6_FW_FLUSH                           = 0x20
	IPV6_FW_GET                             = 0x22
	IPV6_FW_ZERO                            = 0x21
	IPV6_HLIMDEC                            = 0x1
	IPV6_HOPLIMIT                           = 0x2f
	IPV6_HOPOPTS                            = 0x31
	IPV6_IPSEC_POLICY                       = 0x1c
	IPV6_JOIN_GROUP                         = 0xc
	IPV6_LEAVE_GROUP                        = 0xd
	IPV6_MAXHLIM                            = 0xff
	IPV6_MAXOPTHDR                          = 0x800
	IPV6_MAXPACKET                          = 0xffff
	IPV6_MAX_GROUP_SRC_FILTER               = 0x200
	IPV6_MAX_MEMBERSHIPS                    = 0xfff
	IPV6_MAX_SOCK_SRC_FILTER                = 0x80
	IPV6_MIN_MEMBERSHIPS                    = 0x1f
	IPV6_MMTU                               = 0x500
	IPV6_MSFILTER                           = 0x4a
	IPV6_MULTICAST_HOPS                     = 0xa
	IPV6_MULTICAST_IF                       = 0x9
	IPV6_MULTICAST_LOOP                     = 0xb
	IPV6_NEXTHOP                            = 0x30
	IPV6_PATHMTU                            = 0x2c
	IPV6_PKTINFO                            = 0x2e
	IPV6_PORTRANGE                          = 0xe
	IPV6_PORTRANGE_DEFAULT                  = 0x0
	IPV6_PORTRANGE_HIGH                     = 0x1
	IPV6_PORTRANGE_LOW                      = 0x2
	IPV6_PREFER_TEMPADDR                    = 0x3f
	IPV6_RECVDSTOPTS                        = 0x28
	IPV6_RECVHOPLIMIT                       = 0x25
	IPV6_RECVHOPOPTS                        = 0x27
	IPV6_RECVPATHMTU                        = 0x2b
	IPV6_RECVPKTINFO                        = 0x3d
	IPV6_RECVRTHDR                          = 0x26
	IPV6_RECVTCLASS                         = 0x23
	IPV6_RTHDR                              = 0x33
	IPV6_RTHDRDSTOPTS                       = 0x39
	IPV6_RTHDR_LOOSE                        = 0x0
	IPV6_RTHDR_STRICT                       = 0x1
	IPV6_RTHDR_TYPE_0                       = 0x0
	IPV6_SOCKOPT_RESERVED1                  = 0x3
	IPV6_TCLASS                             = 0x24
	IPV6_UNICAST_HOPS                       = 0x4
	IPV6_USE_MIN_MTU                        = 0x2a
	IPV6_V6ONLY                             = 0x1b
	IPV6_VERSION                            = 0x60
	IPV6_VERSION_MASK                       = 0xf0
	IP_ADD_MEMBERSHIP                       = 0xc
	IP_ADD_SOURCE_MEMBERSHIP                = 0x46
	IP_BLOCK_SOURCE                         = 0x48
	IP_BOUND_IF                             = 0x19
	IP_DEFAULT_MULTICAST_LOOP               = 0x1
	IP_DEFAULT_MULTICAST_TTL                = 0x1
	IP_DF                                   = 0x4000
	IP_DONTFRAG                             = 0x1c
	IP_DROP_MEMBERSHIP                      = 0xd
	IP_DROP_SOURCE_MEMBERSHIP               = 0x47
	IP_DUMMYNET_CONFIGURE                   = 0x3c
	IP_DUMMYNET_DEL                         = 0x3d
	IP_DUMMYNET_FLUSH                       = 0x3e
	IP_DUMMYNET_GET                         = 0x40
	IP_FAITH                                = 0x16
	IP_FW_ADD                               = 0x28
	IP_FW_DEL                               = 0x29
	IP_FW_FLUSH                             = 0x2a
	IP_FW_GET                               = 0x2c
	IP_FW_RESETLOG                          = 0x2d
	IP_FW_ZERO                              = 0x2b
	IP_HDRINCL                              = 0x2
	IP_IPSEC_POLICY                         = 0x15
	IP_MAXPACKET                            = 0xffff
	IP_MAX_GROUP_SRC_FILTER                 = 0x200
	IP_MAX_MEMBERSHIPS                      = 0xfff
	IP_MAX_SOCK_MUTE_FILTER                 = 0x80
	IP_MAX_SOCK_SRC_FILTER                  = 0x80
	IP_MF                                   = 0x2000
	IP_MIN_MEMBERSHIPS                      = 0x1f
	IP_MSFILTER                             = 0x4a
	IP_MSS                                  = 0x240
	IP_MULTICAST_IF                         = 0x9
	IP_MULTICAST_IFINDEX                    = 0x42
	IP_MULTICAST_LOOP                       = 0xb
	IP_MULTICAST_TTL                        = 0xa
	IP_MULTICAST_VIF                        = 0xe
	IP_NAT__XXX                             = 0x37
	IP_OFFMASK                              = 0x1fff
	IP_OLD_FW_ADD                           = 0x32
	IP_OLD_FW_DEL                           = 0x33
	IP_OLD_FW_FLUSH                         = 0x34
	IP_OLD_FW_GET                           = 0x36
	IP_OLD_FW_RESETLOG                      = 0x38
	IP_OLD_FW_ZERO                          = 0x35
	IP_OPTIONS                              = 0x1
	IP_PKTINFO                              = 0x1a
	IP_PORTRANGE                            = 0x13
	IP_PORTRANGE_DEFAULT                    = 0x0
	IP_PORTRANGE_HIGH                       = 0x1
	IP_PORTRANGE_LOW                        = 0x2
	IP_RECVDSTADDR                          = 0x7
	IP_RECVIF                               = 0x14
	IP_RECVOPTS                             = 0x5
	IP_RECVPKTINFO                          = 0x1a
	IP_RECVRETOPTS                          = 0x6
	IP_RECVTOS                              = 0x1b
	IP_RECVTTL                              = 0x18
	IP_RETOPTS                              = 0x8
	IP_RF                                   = 0x8000
	IP_RSVP_OFF                             = 0x10
	IP_RSVP_ON                              = 0xf
	IP_RSVP_VIF_OFF                         = 0x12
	IP_RSVP_VIF_ON                          = 0x11
	IP_STRIPHDR                             = 0x17
	IP_TOS                                  = 0x3
	IP_TRAFFIC_MGT_BACKGROUND               = 0x41
	IP_TTL                                  = 0x4
	IP_UNBLOCK_SOURCE                       = 0x49
	ISIG                                    = 0x80
	ISTRIP                                  = 0x20
	IUTF8                                   = 0x4000
	IXANY                                   = 0x800
	IXOFF                                   = 0x400
	IXON                                    = 0x200
	KERN_HOSTNAME                           = 0xa
	KERN_OSRELEASE                          = 0x2
	KERN_OSTYPE                             = 0x1
	KERN_VERSION                            = 0x4
	LOCAL_PEERCRED                          = 0x1
	LOCAL_PEEREPID                          = 0x3
	LOCAL_PEEREUUID                         = 0x5
	LOCAL_PEERPID                           = 0x2
	LOCAL_PEERTOKEN                         = 0x6
	LOCAL_PEERUUID                          = 0x4
	LOCK_EX                                 = 0x2
	LOCK_NB                                 = 0x4
	LOCK_SH                                 = 0x1
	LOCK_UN                                 = 0x8
	MADV_CAN_REUSE                          = 0x9
	MADV_DONTNEED                           = 0x4
	MADV_FREE                               = 0x5
	MADV_FREE_REUSABLE                      = 0x7
	MADV_FREE_REUSE                         = 0x8
	MADV_NORMAL                             = 0x0
	MADV_PAGEOUT                            = 0xa
	MADV_RANDOM                             = 0x1
	MADV_SEQUENTIAL                         = 0x2
	MADV_WILLNEED                           = 0x3
	MADV_ZERO_WIRED_PAGES                   = 0x6
	MAP_32BIT                               = 0x8000
	MAP_ANON                                = 0x1000
	MAP_ANONYMOUS                           = 0x1000
	MAP_COPY                                = 0x2
	MAP_FILE                                = 0x0
	MAP_FIXED                               = 0x10
	MAP_HASSEMAPHORE                        = 0x200
	MAP_JIT                                 = 0x800
	MAP_NOCACHE                             = 0x400
	MAP_NOEXTEND                            = 0x100
	MAP_NORESERVE                           = 0x40
	MAP_PRIVATE                             = 0x2
	MAP_RENAME                              = 0x20
	MAP_RESERVED0080                        = 0x80
	MAP_RESILIENT_CODESIGN                  = 0x2000
	MAP_RESILIENT_MEDIA                     = 0x4000
	MAP_SHARED                              = 0x1
	MAP_TRANSLATED_ALLOW_EXECUTE            = 0x20000
	MAP_UNIX03                              = 0x40000
	MCAST_BLOCK_SOURCE                      = 0x54
	MCAST_EXCLUDE                           = 0x2
	MCAST_INCLUDE                           = 0x1
	MCAST_JOIN_GROUP                        = 0x50
	MCAST_JOIN_SOURCE_GROUP                 = 0x52
	MCAST_LEAVE_GROUP                       = 0x51
	MCAST_LEAVE_SOURCE_GROUP                = 0x53
	MCAST_UNBLOCK_SOURCE                    = 0x55
	MCAST_UNDEFINED                         = 0x0
	MCL_CURRENT                             = 0x1
	MCL_FUTURE                              = 0x2
	MNT_ASYNC                               = 0x40
	MNT_AUTOMOUNTED                         = 0x400000
	MNT_CMDFLAGS                            = 0xf0000
	MNT_CPROTECT                            = 0x80
	MNT_DEFWRITE                            = 0x2000000
	MNT_DONTBROWSE                          = 0x100000
	MNT_DOVOLFS                             = 0x8000
	MNT_DWAIT                               = 0x4
	MNT_EXPORTED                            = 0x100
	MNT_EXT_ROOT_DATA_VOL                   = 0x1
	MNT_FORCE                               = 0x80000
	MNT_IGNORE_OWNERSHIP                    = 0x200000
	MNT_JOURNALED                           = 0x800000
	MNT_LOCAL                               = 0x1000
	MNT_MULTILABEL                          = 0x4000000
	MNT_NOATIME                             = 0x10000000
	MNT_NOBLOCK                             = 0x20000
	MNT_NODEV                               = 0x10
	MNT_NOEXEC                              = 0x4
	MNT_NOSUID                              = 0x8
	MNT_NOUSERXATTR                         = 0x1000000
	MNT_NOWAIT                              = 0x2
	MNT_QUARANTINE                          = 0x400
	MNT_QUOTA                               = 0x2000
	MNT_RDONLY                              = 0x1
	MNT_RELOAD                              = 0x40000
	MNT_REMOVABLE                           = 0x200
	MNT_ROOTFS                              = 0x4000
	MNT_SNAPSHOT                            = 0x40000000
	MNT_STRICTATIME                         = 0x80000000
	MNT_SYNCHRONOUS                         = 0x2
	MNT_UNION                               = 0x20
	MNT_UNKNOWNPERMISSIONS                  = 0x200000
	MNT_UPDATE                              = 0x10000
	MNT_VISFLAGMASK                         = 0xd7f0f7ff
	MNT_WAIT                                = 0x1
	MSG_CTRUNC                              = 0x20
	MSG_DONTROUTE                           = 0x4
	MSG_DONTWAIT                            = 0x80
	MSG_EOF                                 = 0x100
	MSG_EOR                                 = 0x8
	MSG_FLUSH                               = 0x400
	MSG_HAVEMORE                            = 0x2000
	MSG_HOLD                                = 0x800
	MSG_NEEDSA                              = 0x10000
	MSG_NOSIGNAL                            = 0x80000
	MSG_OOB                                 = 0x1
	MSG_PEEK                                = 0x2
	MSG_RCVMORE                             = 0x4000
	MSG_SEND                                = 0x1000
	MSG_TRUNC                               = 0x10
	MSG_WAITALL                             = 0x40
	MSG_WAITSTREAM                          = 0x200
	MS_ASYNC                                = 0x1
	MS_DEACTIVATE                           = 0x8
	MS_INVALIDATE                           = 0x2
	MS_KILLPAGES                            = 0x4
	MS_SYNC                                 = 0x10
	NAME_MAX                                = 0xff
	NET_RT_DUMP                             = 0x1
	NET_RT_DUMP2                            = 0x7
	NET_RT_FLAGS                            = 0x2
	NET_RT_FLAGS_PRIV                       = 0xa
	NET_RT_IFLIST                           = 0x3
	NET_RT_IFLIST2                          = 0x6
	NET_RT_MAXID                            = 0xb
	NET_RT_STAT                             = 0x4
	NET_RT_TRASH                            = 0x5
	NFDBITS                                 = 0x20
	NL0                                     = 0x0
	NL1                                     = 0x100
	NL2                                     = 0x200
	NL3                                     = 0x300
	NLDLY                                   = 0x300
	NOFLSH                                  = 0x80000000
	NOKERNINFO                              = 0x2000000
	NOTE_ABSOLUTE                           = 0x8
	NOTE_ATTRIB                             = 0x8
	NOTE_BACKGROUND                         = 0x40
	NOTE_CHILD                              = 0x4
	NOTE_CRITICAL                           = 0x20
	NOTE_DELETE                             = 0x1
	NOTE_EXEC                               = 0x20000000
	NOTE_EXIT                               = 0x80000000
	NOTE_EXITSTATUS                         = 0x4000000
	NOTE_EXIT_CSERROR                       = 0x40000
	NOTE_EXIT_DECRYPTFAIL                   = 0x10000
	NOTE_EXIT_DETAIL                        = 0x2000000
	NOTE_EXIT_DETAIL_MASK                   = 0x70000
	NOTE_EXIT_MEMORY                        = 0x20000
	NOTE_EXIT_REPARENTED                    = 0x80000
	NOTE_EXTEND                             = 0x4
	NOTE_FFAND                              = 0x40000000
	NOTE_FFCOPY                             = 0xc0000000
	NOTE_FFCTRLMASK                         = 0xc0000000
	NOTE_FFLAGSMASK                         = 0xffffff
	NOTE_FFNOP                              = 0x0
	NOTE_FFOR                               = 0x80000000
	NOTE_FORK                               = 0x40000000
	NOTE_FUNLOCK                            = 0x100
	NOTE_LEEWAY                             = 0x10
	NOTE_LINK                               = 0x10
	NOTE_LOWAT                              = 0x1
	NOTE_MACHTIME                           = 0x100
	NOTE_MACH_CONTINUOUS_TIME               = 0x80
	NOTE_NONE                               = 0x80
	NOTE_NSECONDS                           = 0x4
	NOTE_OOB                                = 0x2
	NOTE_PCTRLMASK                          = -0x100000
	NOTE_PDATAMASK                          = 0xfffff
	NOTE_REAP                               = 0x10000000
	NOTE_RENAME                             = 0x20
	NOTE_REVOKE                             = 0x40
	NOTE_SECONDS                            = 0x1
	NOTE_SIGNAL                             = 0x8000000
	NOTE_TRACK                              = 0x1
	NOTE_TRACKERR                           = 0x2
	NOTE_TRIGGER                            = 0x1000000
	NOTE_USECONDS                           = 0x2
	NOTE_VM_ERROR                           = 0x10000000
	NOTE_VM_PRESSURE                        = 0x80000000
	NOTE_VM_PRESSURE_SUDDEN_TERMINATE       = 0x20000000
	NOTE_VM_PRESSURE_TERMINATE              = 0x40000000
	NOTE_WRITE                              = 0x2
	OCRNL                                   = 0x10
	OFDEL                                   = 0x20000
	OFILL                                   = 0x80
	ONLCR                                   = 0x2
	ONLRET                                  = 0x40
	ONOCR                                   = 0x20
	ONOEOT                                  = 0x8
	OPOST                                   = 0x1
	OXTABS                                  = 0x4
	O_ACCMODE                               = 0x3
	O_ALERT                                 = 0x20000000
	O_APPEND                                = 0x8
	O_ASYNC                                 = 0x40
	O_CLOEXEC                               = 0x1000000
	O_CREAT                                 = 0x200
	O_DIRECTORY                             = 0x100000
	O_DP_GETRAWENCRYPTED                    = 0x1
	O_DP_GETRAWUNENCRYPTED                  = 0x2
	O_DSYNC                                 = 0x400000
	O_EVTONLY                               = 0x8000
	O_EXCL                                  = 0x800
	O_EXLOCK                                = 0x20
	O_FSYNC                                 = 0x80
	O_NDELAY                                = 0x4
	O_NOCTTY                                = 0x20000
	O_NOFOLLOW                              = 0x100
	O_NOFOLLOW_ANY                          = 0x20000000
	O_NONBLOCK                              = 0x4
	O_POPUP                                 = 0x80000000
	O_RDONLY                                = 0x0
	O_RDWR                                  = 0x2
	O_SHLOCK                                = 0x10
	O_SYMLINK                               = 0x200000
	O_SYNC                                  = 0x80
	O_TRUNC                                 = 0x400
	O_WRONLY                                = 0x1
	PARENB                                  = 0x1000
	PARMRK                                  = 0x8
	PARODD                                  = 0x2000
	PENDIN                                  = 0x20000000
	PRIO_PGRP                               = 0x1
	PRIO_PROCESS                            = 0x0
	PRIO_USER                               = 0x2
	PROT_EXEC                               = 0x4
	PROT_NONE                               = 0x0
	PROT_READ                               = 0x1
	PROT_WRITE                              = 0x2
	PT_ATTACH                               = 0xa
	PT_ATTACHEXC                            = 0xe
	PT_CONTINUE                             = 0x7
	PT_DENY_ATTACH                          = 0x1f
	PT_DETACH                               = 0xb
	PT_FIRSTMACH                            = 0x20
	PT_FORCEQUOTA                           = 0x1e
	PT_KILL                                 = 0x8
	PT_READ_D                               = 0x2
	PT_READ_I                               = 0x1
	PT_READ_U                               = 0x3
	PT_SIGEXC                               = 0xc
	PT_STEP                                 = 0x9
	PT_THUPDATE                             = 0xd
	PT_TRACE_ME                             = 0x0
	PT_WRITE_D                              = 0x5
	PT_WRITE_I                              = 0x4
	PT_WRITE_U                              = 0x6
	RENAME_EXCL                             = 0x4
	RENAME_NOFOLLOW_ANY                     = 0x10
	RENAME_RESERVED1                        = 0x8
	RENAME_SECLUDE                          = 0x1
	RENAME_SWAP                             = 0x2
	RLIMIT_AS                               = 0x5
	RLIMIT_CORE                             = 0x4
	RLIMIT_CPU                              = 0x0
	RLIMIT_CPU_USAGE_MONITOR                = 0x2
	RLIMIT_DATA                             = 0x2
	RLIMIT_FSIZE                            = 0x1
	RLIMIT_MEMLOCK                          = 0x6
	RLIMIT_NOFILE                           = 0x8
	RLIMIT_NPROC                            = 0x7
	RLIMIT_RSS                              = 0x5
	RLIMIT_STACK                            = 0x3
	RLIM_INFINITY                           = 0x7fffffffffffffff
	RTAX_AUTHOR                             = 0x6
	RTAX_BRD                                = 0x7
	RTAX_DST                                = 0x0
	RTAX_GATEWAY                            = 0x1
	RTAX_GENMASK                            = 0x3
	RTAX_IFA                                = 0x5
	RTAX_IFP                                = 0x4
	RTAX_MAX                                = 0x8
	RTAX_NETMASK                            = 0x2
	RTA_AUTHOR                              = 0x40
	RTA_BRD                                 = 0x80
	RTA_DST                                 = 0x1
	RTA_GATEWAY                             = 0x2
	RTA_GENMASK                             = 0x8
	RTA_IFA                                 = 0x20
	RTA_IFP                                 = 0x10
	RTA_NETMASK                             = 0x4
	RTF_BLACKHOLE                           = 0x1000
	RTF_BROADCAST                           = 0x400000
	RTF_CLONING                             = 0x100
	RTF_CONDEMNED                           = 0x2000000
	RTF_DEAD                                = 0x20000000
	RTF_DELCLONE                            = 0x80
	RTF_DONE                                = 0x40
	RTF_DYNAMIC                             = 0x10
	RTF_GATEWAY                             = 0x2
	RTF_GLOBAL                              = 0x40000000
	RTF_HOST                                = 0x4
	RTF_IFREF                               = 0x4000000
	RTF_IFSCOPE                             = 0x1000000
	RTF_LLDATA                              = 0x400
	RTF_LLINFO                              = 0x400
	RTF_LOCAL                               = 0x200000
	RTF_MODIFIED                            = 0x20
	RTF_MULTICAST                           = 0x800000
	RTF_NOIFREF                             = 0x2000
	RTF_PINNED                              = 0x100000
	RTF_PRCLONING                           = 0x10000
	RTF_PROTO1                              = 0x8000
	RTF_PROTO2                              = 0x4000
	RTF_PROTO3                              = 0x40000
	RTF_PROXY                               = 0x8000000
	RTF_REJECT                              = 0x8
	RTF_ROUTER                              = 0x10000000
	RTF_STATIC                              = 0x800
	RTF_UP                                  = 0x1
	RTF_WASCLONED                           = 0x20000
	RTF_XRESOLVE                            = 0x200
	RTM_ADD                                 = 0x1
	RTM_CHANGE                              = 0x3
	RTM_DELADDR                             = 0xd
	RTM_DELETE                              = 0x2
	RTM_DELMADDR                            = 0x10
	RTM_GET                                 = 0x4
	RTM_GET2                                = 0x14
	RTM_IFINFO                              = 0xe
	RTM_IFINFO2                             = 0x12
	RTM_LOCK                                = 0x8
	RTM_LOSING                              = 0x5
	RTM_MISS                                = 0x7
	RTM_NEWADDR                             = 0xc
	RTM_NEWMADDR                            = 0xf
	RTM_NEWMADDR2                           = 0x13
	RTM_OLDADD                              = 0x9
	RTM_OLDDEL                              = 0xa
	RTM_REDIRECT                            = 0x6
	RTM_RESOLVE                             = 0xb
	RTM_RTTUNIT                             = 0xf4240
	RTM_VERSION                             = 0x5
	RTV_EXPIRE                              = 0x4
	RTV_HOPCOUNT                            = 0x2
	RTV_MTU                                 = 0x1
	RTV_RPIPE                               = 0x8
	RTV_RTT                                 = 0x40
	RTV_RTTVAR                              = 0x80
	RTV_SPIPE                               = 0x10
	RTV_SSTHRESH                            = 0x20
	RUSAGE_CHILDREN                         = -0x1
	RUSAGE_SELF                             = 0x0
	SAE_ASSOCID_ALL                         = 0xffffffff
	SAE_ASSOCID_ANY                         = 0x0
	SAE_CONNID_ALL                          = 0xffffffff
	SAE_CONNID_ANY                          = 0x0
	SCM_CREDS                               = 0x3
	SCM_RIGHTS                              = 0x1
	SCM_TIMESTAMP                           = 0x2
	SCM_TIMESTAMP_MONOTONIC                 = 0x4
	SEEK_CUR                                = 0x1
	SEEK_DATA                               = 0x4
	SEEK_END                                = 0x2
	SEEK_HOLE                               = 0x3
	SEEK_SET                                = 0x0
	SF_APPEND                               = 0x40000
	SF_ARCHIVED                             = 0x10000
	SF_DATALESS                             = 0x40000000
	SF_FIRMLINK                             = 0x800000
	SF_IMMUTABLE                            = 0x20000
	SF_NOUNLINK                             = 0x100000
	SF_RESTRICTED                           = 0x80000
	SF_SETTABLE                             = 0x3fff0000
	SF_SUPPORTED                            = 0x9f0000
	SF_SYNTHETIC                            = 0xc0000000
	SHUT_RD                                 = 0x0
	SHUT_RDWR                               = 0x2
	SHUT_WR                                 = 0x1
	SIOCADDMULTI                            = 0x80206931
	SIOCAIFADDR                             = 0x8040691a
	SIOCARPIPLL                             = 0xc0206928
	SIOCATMARK                              = 0x40047307
	SIOCAUTOADDR                            = 0xc0206926
	SIOCAUTONETMASK                         = 0x80206927
	SIOCDELMULTI                            = 0x80206932
	SIOCDIFADDR                             = 0x80206919
	SIOCDIFPHYADDR                          = 0x80206941
	SIOCGDRVSPEC                            = 0xc028697b
	SIOCGETVLAN                             = 0xc020697f
	SIOCGHIWAT                              = 0x40047301
	SIOCGIF6LOWPAN                          = 0xc02069c5
	SIOCGIFADDR                             = 0xc0206921
	SIOCGIFALTMTU                           = 0xc0206948
	SIOCGIFASYNCMAP                         = 0xc020697c
	SIOCGIFBOND                             = 0xc0206947
	SIOCGIFBRDADDR                          = 0xc0206923
	SIOCGIFCAP                              = 0xc020695b
	SIOCGIFCONF                             = 0xc00c6924
	SIOCGIFDEVMTU                           = 0xc0206944
	SIOCGIFDSTADDR                          = 0xc0206922
	SIOCGIFFLAGS                            = 0xc0206911
	SIOCGIFFUNCTIONALTYPE                   = 0xc02069ad
	SIOCGIFGENERIC                          = 0xc020693a
	SIOCGIFKPI                              = 0xc0206987
	SIOCGIFMAC                              = 0xc0206982
	SIOCGIFMEDIA                            = 0xc02c6938
	SIOCGIFMETRIC                           = 0xc0206917
	SIOCGIFMTU                              = 0xc0206933
	SIOCGIFNETMASK                          = 0xc0206925
	SIOCGIFPDSTADDR                         = 0xc0206940
	SIOCGIFPHYS                             = 0xc0206935
	SIOCGIFPSRCADDR                         = 0xc020693f
	SIOCGIFSTATUS                           = 0xc331693d
	SIOCGIFVLAN                             = 0xc020697f
	SIOCGIFWAKEFLAGS                        = 0xc0206988
	SIOCGIFXMEDIA                           = 0xc02c6948
	SIOCGLOWAT                              = 0x40047303
	SIOCGPGRP                               = 0x40047309
	SIOCIFCREATE                            = 0xc0206978
	SIOCIFCREATE2                           = 0xc020697a
	SIOCIFDESTROY                           = 0x80206979
	SIOCIFGCLONERS                          = 0xc0106981
	SIOCRSLVMULTI                           = 0xc010693b
	SIOCSDRVSPEC                            = 0x8028697b
	SIOCSETVLAN                             = 0x8020697e
	SIOCSHIWAT                              = 0x80047300
	SIOCSIF6LOWPAN                          = 0x802069c4
	SIOCSIFADDR                             = 0x8020690c
	SIOCSIFALTMTU                           = 0x80206945
	SIOCSIFASYNCMAP                         = 0x8020697d
	SIOCSIFBOND                             = 0x80206946
	SIOCSIFBRDADDR                          = 0x80206913
	SIOCSIFCAP                              = 0x8020695a
	SIOCSIFDSTADDR                          = 0x8020690e
	SIOCSIFFLAGS                            = 0x80206910
	SIOCSIFGENERIC                          = 0x80206939
	SIOCSIFKPI                              = 0x80206986
	SIOCSIFLLADDR                           = 0x8020693c
	SIOCSIFMAC                              = 0x80206983
	SIOCSIFMEDIA                            = 0xc0206937
	SIOCSIFMETRIC                           = 0x80206918
	SIOCSIFMTU                              = 0x80206934
	SIOCSIFNETMASK                          = 0x80206916
	SIOCSIFPHYADDR                          = 0x8040693e
	SIOCSIFPHYS                             = 0x80206936
	SIOCSIFVLAN                             = 0x8020697e
	SIOCSLOWAT                              = 0x80047302
	SIOCSPGRP                               = 0x80047308
	SOCK_DGRAM                              = 0x2
	SOCK_MAXADDRLEN                         = 0xff
	SOCK_RAW                                = 0x3
	SOCK_RDM                                = 0x4
	SOCK_SEQPACKET                          = 0x5
	SOCK_STREAM                             = 0x1
	SOL_LOCAL                               = 0x0
	SOL_SOCKET                              = 0xffff
	SOMAXCONN                               = 0x80
	SO_ACCEPTCONN                           = 0x2
	SO_BROADCAST                            = 0x20
	SO_DEBUG                                = 0x1
	SO_DONTROUTE                            = 0x10
	SO_DONTTRUNC                            = 0x2000
	SO_ERROR                                = 0x1007
	SO_KEEPALIVE                            = 0x8
	SO_LABEL                                = 0x1010
	SO_LINGER                               = 0x80
	SO_LINGER_SEC                           = 0x1080
	SO_NETSVC_MARKING_LEVEL                 = 0x1119
	SO_NET_SERVICE_TYPE                     = 0x1116
	SO_NKE                                  = 0x1021
	SO_NOADDRERR                            = 0x1023
	SO_NOSIGPIPE                            = 0x1022
	SO_NOTIFYCONFLICT                       = 0x1026
	SO_NP_EXTENSIONS                        = 0x1083
	SO_NREAD                                = 0x1020
	SO_NUMRCVPKT                            = 0x1112
	SO_NWRITE                               = 0x1024
	SO_OOBINLINE                            = 0x100
	SO_PEERLABEL                            = 0x1011
	SO_RANDOMPORT                           = 0x1082
	SO_RCVBUF                               = 0x1002
	SO_RCVLOWAT                             = 0x1004
	SO_RCVTIMEO                             = 0x1006
	SO_REUSEADDR                            = 0x4
	SO_REUSEPORT                            = 0x200
	SO_REUSESHAREUID                        = 0x1025
	SO_SNDBUF                               = 0x1001
	SO_SNDLOWAT                             = 0x1003
	SO_SNDTIMEO                             = 0x1005
	SO_TIMESTAMP                            = 0x400
	SO_TIMESTAMP_MONOTONIC                  = 0x800
	SO_TRACKER_ATTRIBUTE_FLAGS_APP_APPROVED = 0x1
	SO_TRACKER_ATTRIBUTE_FLAGS_DOMAIN_SHORT = 0x4
	SO_TRACKER_ATTRIBUTE_FLAGS_TRACKER      = 0x2
	SO_TRACKER_TRANSPARENCY_VERSION         = 0x3
	SO_TYPE                                 = 0x1008
	SO_UPCALLCLOSEWAIT                      = 0x1027
	SO_USELOOPBACK                          = 0x40
	SO_WANTMORE                             = 0x4000
	SO_WANTOOBFLAG                          = 0x8000
	S_IEXEC                                 = 0x40
	S_IFBLK                                 = 0x6000
	S_IFCHR                                 = 0x2000
	S_IFDIR                                 = 0x4000
	S_IFIFO                                 = 0x1000
	S_IFLNK                                 = 0xa000
	S_IFMT                                  = 0xf000
	S_IFREG                                 = 0x8000
	S_IFSOCK                                = 0xc000
	S_IFWHT                                 = 0xe000
	S_IREAD                                 = 0x100
	S_IRGRP                                 = 0x20
	S_IROTH                                 = 0x4
	S_IRUSR                                 = 0x100
	S_IRWXG                                 = 0x38
	S_IRWXO                                 = 0x7
	S_IRWXU                                 = 0x1c0
	S_ISGID                                 = 0x400
	S_ISTXT                                 = 0x200
	S_ISUID                                 = 0x800
	S_ISVTX                                 = 0x200
	S_IWGRP                                 = 0x10
	S_IWOTH                                 = 0x2
	S_IWRITE                                = 0x80
	S_IWUSR                                 = 0x80
	S_IXGRP                                 = 0x8
	S_IXOTH                                 = 0x1
	S_IXUSR                                 = 0x40
	TAB0                                    = 0x0
	TAB1                                    = 0x400
	TAB2                                    = 0x800
	TAB3                                    = 0x4
	TABDLY                                  = 0xc04
	TCIFLUSH                                = 0x1
	TCIOFF                                  = 0x3
	TCIOFLUSH                               = 0x3
	TCION                                   = 0x4
	TCOFLUSH                                = 0x2
	TCOOFF                                  = 0x1
	TCOON                                   = 0x2
	TCPOPT_CC                               = 0xb
	TCPOPT_CCECHO                           = 0xd
	TCPOPT_CCNEW                            = 0xc
	TCPOPT_EOL                              = 0x0
	TCPOPT_FASTOPEN                         = 0x22
	TCPOPT_MAXSEG                           = 0x2
	TCPOPT_NOP                              = 0x1
	TCPOPT_SACK                             = 0x5
	TCPOPT_SACK_HDR                         = 0x1010500
	TCPOPT_SACK_PERMITTED                   = 0x4
	TCPOPT_SACK_PERMIT_HDR                  = 0x1010402
	TCPOPT_SIGNATURE                        = 0x13
	TCPOPT_TIMESTAMP                        = 0x8
	TCPOPT_TSTAMP_HDR                       = 0x101080a
	TCPOPT_WINDOW                           = 0x3
	TCP_CONNECTIONTIMEOUT                   = 0x20
	TCP_CONNECTION_INFO                     = 0x106
	TCP_ENABLE_ECN                          = 0x104
	TCP_FASTOPEN                            = 0x105
	TCP_KEEPALIVE                           = 0x10
	TCP_KEEPCNT                             = 0x102
	TCP_KEEPINTVL                           = 0x101
	TCP_MAXHLEN                             = 0x3c
	TCP_MAXOLEN                             = 0x28
	TCP_MAXSEG                              = 0x2
	TCP_MAXWIN                              = 0xffff
	TCP_MAX_SACK                            = 0x4
	TCP_MAX_WINSHIFT                        = 0xe
	TCP_MINMSS                              = 0xd8
	TCP_MSS                                 = 0x200
	TCP_NODELAY                             = 0x1
	TCP_NOOPT                               = 0x8
	TCP_NOPUSH                              = 0x4
	TCP_NOTSENT_LOWAT                       = 0x201
	TCP_RXT_CONNDROPTIME                    = 0x80
	TCP_RXT_FINDROP                         = 0x100
	TCP_SENDMOREACKS                        = 0x103
	TCSAFLUSH                               = 0x2
	TIOCCBRK                                = 0x2000747a
	TIOCCDTR                                = 0x20007478
	TIOCCONS                                = 0x80047462
	TIOCDCDTIMESTAMP                        = 0x40107458
	TIOCDRAIN                               = 0x2000745e
	TIOCDSIMICROCODE                        = 0x20007455
	TIOCEXCL                                = 0x2000740d
	TIOCEXT                                 = 0x80047460
	TIOCFLUSH                               = 0x80047410
	TIOCGDRAINWAIT                          = 0x40047456
	TIOCGETA                                = 0x40487413
	TIOCGETD                                = 0x4004741a
	TIOCGPGRP                               = 0x40047477
	TIOCGWINSZ                              = 0x40087468
	TIOCIXOFF                               = 0x20007480
	TIOCIXON                                = 0x20007481
	TIOCMBIC                                = 0x8004746b
	TIOCMBIS                                = 0x8004746c
	TIOCMGDTRWAIT                           = 0x4004745a
	TIOCMGET                                = 0x4004746a
	TIOCMODG                                = 0x40047403
	TIOCMODS                                = 0x80047404
	TIOCMSDTRWAIT                           = 0x8004745b
	TIOCMSET                                = 0x8004746d
	TIOCM_CAR                               = 0x40
	TIOCM_CD                                = 0x40
	TIOCM_CTS                               = 0x20
	TIOCM_DSR                               = 0x100
	TIOCM_DTR                               = 0x2
	TIOCM_LE                                = 0x1
	TIOCM_RI                                = 0x80
	TIOCM_RNG                               = 0x80
	TIOCM_RTS                               = 0x4
	TIOCM_SR                                = 0x10
	TIOCM_ST                                = 0x8
	TIOCNOTTY                               = 0x20007471
	TIOCNXCL                                = 0x2000740e
	TIOCOUTQ                                = 0x40047473
	TIOCPKT                                 = 0x80047470
	TIOCPKT_DATA                            = 0x0
	TIOCPKT_DOSTOP                          = 0x20
	TIOCPKT_FLUSHREAD                       = 0x1
	TIOCPKT_FLUSHWRITE                      = 0x2
	TIOCPKT_IOCTL                           = 0x40
	TIOCPKT_NOSTOP                          = 0x10
	TIOCPKT_START                           = 0x8
	TIOCPKT_STOP                            = 0x4
	TIOCPTYGNAME                            = 0x40807453
	TIOCPTYGRANT                            = 0x20007454
	TIOCPTYUNLK                             = 0x20007452
	TIOCREMOTE                              = 0x80047469
	TIOCSBRK                                = 0x2000747b
	TIOCSCONS                               = 0x20007463
	TIOCSCTTY                               = 0x20007461
	TIOCSDRAINWAIT                          = 0x80047457
	TIOCSDTR                                = 0x20007479
	TIOCSETA                                = 0x80487414
	TIOCSETAF                               = 0x80487416
	TIOCSETAW                               = 0x80487415
	TIOCSETD                                = 0x8004741b
	TIOCSIG                                 = 0x2000745f
	TIOCSPGRP                               = 0x80047476
	TIOCSTART                               = 0x2000746e
	TIOCSTAT                                = 0x20007465
	TIOCSTI                                 = 0x80017472
	TIOCSTOP                                = 0x2000746f
	TIOCSWINSZ                              = 0x80087467
	TIOCTIMESTAMP                           = 0x40107459
	TIOCUCNTL                               = 0x80047466
	TOSTOP                                  = 0x400000
	UF_APPEND                               = 0x4
	UF_COMPRESSED                           = 0x20
	UF_DATAVAULT                            = 0x80
	UF_HIDDEN                               = 0x8000
	UF_IMMUTABLE                            = 0x2
	UF_NODUMP                               = 0x1
	UF_OPAQUE                               = 0x8
	UF_SETTABLE                             = 0xffff
	UF_TRACKED                              = 0x40
	VDISCARD                                = 0xf
	VDSUSP                                  = 0xb
	VEOF                                    = 0x0
	VEOL                                    = 0x1
	VEOL2                                   = 0x2
	VERASE                                  = 0x3
	VINTR                                   = 0x8
	VKILL                                   = 0x5
	VLNEXT                                  = 0xe
	VMADDR_CID_ANY                          = 0xffffffff
	VMADDR_CID_HOST                         = 0x2
	VMADDR_CID_HYPERVISOR                   = 0x0
	VMADDR_CID_RESERVED                     = 0x1
	VMADDR_PORT_ANY                         = 0xffffffff
	VMIN                                    = 0x10
	VM_LOADAVG                              = 0x2
	VM_MACHFACTOR                           = 0x4
	VM_MAXID                                = 0x6
	VM_METER                                = 0x1
	VM_SWAPUSAGE                            = 0x5
	VQUIT                                   = 0x9
	VREPRINT                                = 0x6
	VSTART                                  = 0xc
	VSTATUS                                 = 0x12
	VSTOP                                   = 0xd
	VSUSP                                   = 0xa
	VT0                                     = 0x0
	VT1                                     = 0x10000
	VTDLY                                   = 0x10000
	VTIME                                   = 0x11
	VWERASE                                 = 0x4
	WCONTINUED                              = 0x10
	WCOREFLAG                               = 0x80
	WEXITED                                 = 0x4
	WNOHANG                                 = 0x1
	WNOWAIT                                 = 0x20
	WORDSIZE                                = 0x40
	WSTOPPED                                = 0x8
	WUNTRACED                               = 0x2
	XATTR_CREATE                            = 0x2
	XATTR_NODEFAULT                         = 0x10
	XATTR_NOFOLLOW                          = 0x1
	XATTR_NOSECURITY                        = 0x8
	XATTR_REPLACE                           = 0x4
	XATTR_SHOWCOMPRESSION                   = 0x20
)

// Errors
const (
	E2BIG           = syscall.Errno(0x7)
	EACCES          = syscall.Errno(0xd)
	EADDRINUSE      = syscall.Errno(0x30)
	EADDRNOTAVAIL   = syscall.Errno(0x31)
	EAFNOSUPPORT    = syscall.Errno(0x2f)
	EAGAIN          = syscall.Errno(0x23)
	EALREADY        = syscall.Errno(0x25)
	EAUTH           = syscall.Errno(0x50)
	EBADARCH        = syscall.Errno(0x56)
	EBADEXEC        = syscall.Errno(0x55)
	EBADF           = syscall.Errno(0x9)
	EBADMACHO       = syscall.Errno(0x58)
	EBADMSG         = syscall.Errno(0x5e)
	EBADRPC         = syscall.Errno(0x48)
	EBUSY           = syscall.Errno(0x10)
	ECANCELED       = syscall.Errno(0x59)
	ECHILD          = syscall.Errno(0xa)
	ECONNABORTED    = syscall.Errno(0x35)
	ECONNREFUSED    = syscall.Errno(0x3d)
	ECONNRESET      = syscall.Errno(0x36)
	EDEADLK         = syscall.Errno(0xb)
	EDESTADDRREQ    = syscall.Errno(0x27)
	EDEVERR         = syscall.Errno(0x53)
	EDOM            = syscall.Errno(0x21)
	EDQUOT          = syscall.Errno(0x45)
	EEXIST          = syscall.Errno(0x11)
	EFAULT          = syscall.Errno(0xe)
	EFBIG           = syscall.Errno(0x1b)
	EFTYPE          = syscall.Errno(0x4f)
	EHOSTDOWN       = syscall.Errno(0x40)
	EHOSTUNREACH    = syscall.Errno(0x41)
	EIDRM           = syscall.Errno(0x5a)
	EILSEQ          = syscall.Errno(0x5c)
	EINPROGRESS     = syscall.Errno(0x24)
	EINTR           = syscall.Errno(0x4)
	EINVAL          = syscall.Errno(0x16)
	EIO             = syscall.Errno(0x5)
	EISCONN         = syscall.Errno(0x38)
	EISDIR          = syscall.Errno(0x15)
	ELAST           = syscall.Errno(0x6a)
	ELOOP           = syscall.Errno(0x3e)
	EMFILE          = syscall.Errno(0x18)
	EMLINK          = syscall.Errno(0x1f)
	EMSGSIZE        = syscall.Errno(0x28)
	EMULTIHOP       = syscall.Errno(0x5f)
	ENAMETOOLONG    = syscall.Errno(0x3f)
	ENEEDAUTH       = syscall.Errno(0x51)
	ENETDOWN        = syscall.Errno(0x32)
	ENETRESET       = syscall.Errno(0x34)
	ENETUNREACH     = syscall.Errno(0x33)
	ENFILE          = syscall.Errno(0x17)
	ENOATTR         = syscall.Errno(0x5d)
	ENOBUFS         = syscall.Errno(0x37)
	ENODATA         = syscall.Errno(0x60)
	ENODEV          = syscall.Errno(0x13)
	ENOENT          = syscall.Errno(0x2)
	ENOEXEC         = syscall.Errno(0x8)
	ENOLCK          = syscall.Errno(0x4d)
	ENOLINK         = syscall.Errno(0x61)
	ENOMEM          = syscall.Errno(0xc)
	ENOMSG          = syscall.Errno(0x5b)
	ENOPOLICY       = syscall.Errno(0x67)
	ENOPROTOOPT     = syscall.Errno(0x2a)
	ENOSPC          = syscall.Errno(0x1c)
	ENOSR           = syscall.Errno(0x62)
	ENOSTR          = syscall.Errno(0x63)
	ENOSYS          = syscall.Errno(0x4e)
	ENOTBLK         = syscall.Errno(0xf)
	ENOTCONN        = syscall.Errno(0x39)
	ENOTDIR         = syscall.Errno(0x14)
	ENOTEMPTY       = syscall.Errno(0x42)
	ENOTRECOVERABLE = syscall.Errno(0x68)
	ENOTSOCK        = syscall.Errno(0x26)
	ENOTSUP         = syscall.Errno(0x2d)
	ENOTTY          = syscall.Errno(0x19)
	ENXIO           = syscall.Errno(0x6)
	EOPNOTSUPP      = syscall.Errno(0x66)
	EOVERFLOW       = syscall.Errno(0x54)
	EOWNERDEAD      = syscall.Errno(0x69)
	EPERM           = syscall.Errno(0x1)
	EPFNOSUPPORT    = syscall.Errno(0x2e)
	EPIPE           = syscall.Errno(0x20)
	EPROCLIM        = syscall.Errno(0x43)
	EPROCUNAVAIL    = syscall.Errno(0x4c)
	EPROGMISMATCH   = syscall.Errno(0x4b)
	EPROGUNAVAIL    = syscall.Errno(0x4a)
	EPROTO          = syscall.Errno(0x64)
	EPROTONOSUPPORT = syscall.Errno(0x2b)
	EPROTOTYPE      = syscall.Errno(0x29)
	EPWROFF         = syscall.Errno(0x52)
	EQFULL          = syscall.Errno(0x6a)
	ERANGE          = syscall.Errno(0x22)
	EREMOTE         = syscall.Errno(0x47)
	EROFS           = syscall.Errno(0x1e)
	ERPCMISMATCH    = syscall.Errno(0x49)
	ESHLIBVERS      = syscall.Errno(0x57)
	ESHUTDOWN       = syscall.Errno(0x3a)
	ESOCKTNOSUPPORT = syscall.Errno(0x2c)
	ESPIPE          = syscall.Errno(0x1d)
	ESRCH           = syscall.Errno(0x3)
	ESTALE          = syscall.Errno(0x46)
	ETIME           = syscall.Errno(0x65)
	ETIMEDOUT       = syscall.Errno(0x3c)
	ETOOMANYREFS    = syscall.Errno(0x3b)
	ETXTBSY         = syscall.Errno(0x1a)
	EUSERS          = syscall.Errno(0x44)
	EWOULDBLOCK     = syscall.Errno(0x23)
	EXDEV           = syscall.Errno(0x12)
)

// Signals
const (
	SIGABRT   = syscall.Signal(0x6)
	SIGALRM   = syscall.Signal(0xe)
	SIGBUS    = syscall.Signal(0xa)
	SIGCHLD   = syscall.Signal(0x14)
	SIGCONT   = syscall.Signal(0x13)
	SIGEMT    = syscall.Signal(0x7)
	SIGFPE    = syscall.Signal(0x8)
	SIGHUP    = syscall.Signal(0x1)
	SIGILL    = syscall.Signal(0x4)
	SIGINFO   = syscall.Signal(0x1d)
	SIGINT    = syscall.Signal(0x2)
	SIGIO     = syscall.Signal(0x17)
	SIGIOT    = syscall.Signal(0x6)
	SIGKILL   = syscall.Signal(0x9)
	SIGPIPE   = syscall.Signal(0xd)
	SIGPROF   = syscall.Signal(0x1b)
	SIGQUIT   = syscall.Signal(0x3)
	SIGSEGV   = syscall.Signal(0xb)
	SIGSTOP   = syscall.Signal(0x11)
	SIGSYS    = syscall.Signal(0xc)
	SIGTERM   = syscall.Signal(0xf)
	SIGTRAP   = syscall.Signal(0x5)
	SIGTSTP   = syscall.Signal(0x12)
	SIGTTIN   = syscall.Signal(0x15)
	SIGTTOU   = syscall.Signal(0x16)
	SIGURG    = syscall.Signal(0x10)
	SIGUSR1   = syscall.Signal(0x1e)
	SIGUSR2   = syscall.Signal(0x1f)
	SIGVTALRM = syscall.Signal(0x1a)
	SIGWINCH  = syscall.Signal(0x1c)
	SIGXCPU   = syscall.Signal(0x18)
	SIGXFSZ   = syscall.Signal(0x19)
)

// Error table
var errorList = [...]struct {
	num  syscall.Errno
	name string
	desc string
}{
	{1, "EPERM", "operation not permitted"},
	{2, "ENOENT", "no such file or directory"},
	{3, "ESRCH", "no such process"},
	{4, "EINTR", "interrupted system call"},
	{5, "EIO", "input/output error"},
	{6, "ENXIO", "device not configured"},
	{7, "E2BIG", "argument list too long"},
	{8, "ENOEXEC", "exec format error"},
	{9, "EBADF", "bad file descriptor"},
	{10, "ECHILD", "no child processes"},
	{11, "EDEADLK", "resource deadlock avoided"},
	{12, "ENOMEM", "cannot allocate memory"},
	{13, "EACCES", "permission denied"},
	{14, "EFAULT", "bad address"},
	{15, "ENOTBLK", "block device required"},
	{16, "EBUSY", "resource busy"},
	{17, "EEXIST", "file exists"},
	{18, "EXDEV", "cross-device link"},
	{19, "ENODEV", "operation not supported by device"},
	{20, "ENOTDIR", "not a directory"},
	{21, "EISDIR", "is a directory"},
	{22, "EINVAL", "invalid argument"},
	{23, "ENFILE", "too many open files in system"},
	{24, "EMFILE", "too many open files"},
	{25, "ENOTTY", "inappropriate ioctl for device"},
	{26, "ETXTBSY", "text file busy"},
	{27, "EFBIG", "file too large"},
	{28, "ENOSPC", "no space left on device"},
	{29, "ESPIPE", "illegal seek"},
	{30, "EROFS", "read-only file system"},
	{31, "EMLINK", "too many links"},
	{32, "EPIPE", "broken pipe"},
	{33, "EDOM", "numerical argument out of domain"},
	{34, "ERANGE", "result too large"},
	{35, "EAGAIN", "resource temporarily unavailable"},
	{36, "EINPROGRESS", "operation now in progress"},
	{37, "EALREADY", "operation already in progress"},
	{38, "ENOTSOCK", "socket operation on non-socket"},
	{39, "EDESTADDRREQ", "destination address required"},
	{40, "EMSGSIZE", "message too long"},
	{41, "EPROTOTYPE", "protocol wrong type for socket"},
	{42, "ENOPROTOOPT", "protocol not available"},
	{43, "EPROTONOSUPPORT", "protocol not supported"},
	{44, "ESOCKTNOSUPPORT", "socket type not supported"},
	{45, "ENOTSUP", "operation not supported"},
	{46, "EPFNOSUPPORT", "protocol family not supported"},
	{47, "EAFNOSUPPORT", "address family not supported by protocol family"},
	{48, "EADDRINUSE", "address already in use"},
	{49, "EADDRNOTAVAIL", "can't assign requested address"},
	{50, "ENETDOWN", "network is down"},
	{51, "ENETUNREACH", "network is unreachable"},
	{52, "ENETRESET", "network dropped connection on reset"},
	{53, "ECONNABORTED", "software caused connection abort"},
	{54, "ECONNRESET", "connection reset by peer"},
	{55, "ENOBUFS", "no buffer space available"},
	{56, "EISCONN", "socket is already connected"},
	{57, "ENOTCONN", "socket is not connected"},
	{58, "ESHUTDOWN", "can't send after socket shutdown"},
	{59, "ETOOMANYREFS", "too many references: can't splice"},
	{60, "ETIMEDOUT", "operation timed out"},
	{61, "ECONNREFUSED", "connection refused"},
	{62, "ELOOP", "too many levels of symbolic links"},
	{63, "ENAMETOOLONG", "file name too long"},
	{64, "EHOSTDOWN", "host is down"},
	{65, "EHOSTUNREACH", "no route to host"},
	{66, "ENOTEMPTY", "directory not empty"},
	{67, "EPROCLIM", "too many processes"},
	{68, "EUSERS", "too many users"},
	{69, "EDQUOT", "disc quota exceeded"},
	{70, "ESTALE", "stale NFS file handle"},
	{71, "EREMOTE", "too many levels of remote in path"},
	{72, "EBADRPC", "RPC struct is bad"},
	{73, "ERPCMISMATCH", "RPC version wrong"},
	{74, "EPROGUNAVAIL", "RPC prog. not avail"},
	{75, "EPROGMISMATCH", "program version wrong"},
	{76, "EPROCUNAVAIL", "bad procedure for program"},
	{77, "ENOLCK", "no locks available"},
	{78, "ENOSYS", "function not implemented"},
	{79, "EFTYPE", "inappropriate file type or format"},
	{80, "EAUTH", "authentication error"},
	{81, "ENEEDAUTH", "need authenticator"},
	{82, "EPWROFF", "device power is off"},
	{83, "EDEVERR", "device error"},
	{84, "EOVERFLOW", "value too large to be stored in data type"},
	{85, "EBADEXEC", "bad executable (or shared library)"},
	{86, "EBADARCH", "bad CPU type in executable"},
	{87, "ESHLIBVERS", "shared library version mismatch"},
	{88, "EBADMACHO", "malformed Mach-o file"},
	{89, "ECANCELED", "operation canceled"},
	{90, "EIDRM", "identifier removed"},
	{91, "ENOMSG", "no message of desired type"},
	{92, "EILSEQ", "illegal byte sequence"},
	{93, "ENOATTR", "attribute not found"},
	{94, "EBADMSG", "bad message"},
	{95, "EMULTIHOP", "EMULTIHOP (Reserved)"},
	{96, "ENODATA", "no message available on STREAM"},
	{97, "ENOLINK", "ENOLINK (Reserved)"},
	{98, "ENOSR", "no STREAM resources"},
	{99, "ENOSTR", "not a STREAM"},
	{100, "EPROTO", "protocol error"},
	{101, "ETIME", "STREAM ioctl timeout"},
	{102, "EOPNOTSUPP", "operation not supported on socket"},
	{103, "ENOPOLICY", "policy not found"},
	{104, "ENOTRECOVERABLE", "state not recoverable"},
	{105, "EOWNERDEAD", "previous owner died"},
	{106, "EQFULL", "interface output queue is full"},
}

// Signal table
var signalList = [...]struct {
	num  syscall.Signal
	name string
	desc string
}{
	{1, "SIGHUP", "hangup"},
	{2, "SIGINT", "interrupt"},
	{3, "SIGQUIT", "quit"},
	{4, "SIGILL", "illegal instruction"},
	{5, "SIGTRAP", "trace/BPT trap"},
	{6, "SIGABRT", "abort trap"},
	{7, "SIGEMT", "EMT trap"},
	{8, "SIGFPE", "floating point exception"},
	{9, "SIGKILL", "killed"},
	{10, "SIGBUS", "bus error"},
	{11, "SIGSEGV", "segmentation fault"},
	{12, "SIGSYS", "bad system call"},
	{13, "SIGPIPE", "broken pipe"},
	{14, "SIGALRM", "alarm clock"},
	{15, "SIGTERM", "terminated"},
	{16, "SIGURG", "urgent I/O condition"},
	{17, "SIGSTOP", "suspended (signal)"},
	{18, "SIGTSTP", "suspended"},
	{19, "SIGCONT", "continued"},
	{20, "SIGCHLD", "child exited"},
	{21, "SIGTTIN", "stopped (tty input)"},
	{22, "SIGTTOU", "stopped (tty output)"},
	{23, "SIGIO", "I/O possible"},
	{24, "SIGXCPU", "cputime limit exceeded"},
	{25, "SIGXFSZ", "filesize limit exceeded"},
	{26, "SIGVTALRM", "virtual timer expired"},
	{27, "SIGPROF", "profiling timer expired"},
	{28, "SIGWINCH", "window size changes"},
	{29, "SIGINFO", "information request"},
	{30, "SIGUSR1", "user defined signal 1"},
	{31, "SIGUSR2", "user defined signal 2"},
}
