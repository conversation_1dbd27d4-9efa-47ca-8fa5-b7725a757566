// cgo -godefs types_netbsd.go | go run mkpost.go
// Code generated by the command above; see README.md. DO NOT EDIT.

//go:build 386 && netbsd

package unix

const (
	SizeofPtr      = 0x4
	SizeofShort    = 0x2
	SizeofInt      = 0x4
	SizeofLong     = 0x4
	SizeofLongLong = 0x8
)

type (
	_C_short     int16
	_C_int       int32
	_C_long      int32
	_C_long_long int64
)

type Timespec struct {
	Sec  int64
	Nsec int32
}

type Timeval struct {
	Sec  int64
	Usec int32
}

type Rusage struct {
	Utime    Timeval
	Stime    Timeval
	Maxrss   int32
	Ixrss    int32
	Idrss    int32
	Isrss    int32
	Minflt   int32
	Majflt   int32
	Nswap    int32
	Inblock  int32
	Oublock  int32
	Msgsnd   int32
	Msgrcv   int32
	Nsignals int32
	Nvcsw    int32
	Nivcsw   int32
}

type Rlimit struct {
	Cur uint64
	Max uint64
}

type _Gid_t uint32

type Stat_t struct {
	Dev     uint64
	Mode    uint32
	Ino     uint64
	Nlink   uint32
	Uid     uint32
	Gid     uint32
	Rdev    uint64
	Atim    Timespec
	Mtim    Timespec
	Ctim    Timespec
	Btim    Timespec
	Size    int64
	Blocks  int64
	Blksize uint32
	Flags   uint32
	Gen     uint32
	Spare   [2]uint32
}

type Statfs_t [0]byte

type Statvfs_t struct {
	Flag        uint32
	Bsize       uint32
	Frsize      uint32
	Iosize      uint32
	Blocks      uint64
	Bfree       uint64
	Bavail      uint64
	Bresvd      uint64
	Files       uint64
	Ffree       uint64
	Favail      uint64
	Fresvd      uint64
	Syncreads   uint64
	Syncwrites  uint64
	Asyncreads  uint64
	Asyncwrites uint64
	Fsidx       Fsid
	Fsid        uint32
	Namemax     uint32
	Owner       uint32
	Spare       [4]uint32
	Fstypename  [32]byte
	Mntonname   [1024]byte
	Mntfromname [1024]byte
}

type Flock_t struct {
	Start  int64
	Len    int64
	Pid    int32
	Type   int16
	Whence int16
}

type Dirent struct {
	Fileno    uint64
	Reclen    uint16
	Namlen    uint16
	Type      uint8
	Name      [512]int8
	Pad_cgo_0 [3]byte
}

type Fsid struct {
	X__fsid_val [2]int32
}

const (
	PathMax = 0x400
)

const (
	ST_WAIT   = 0x1
	ST_NOWAIT = 0x2
)

const (
	FADV_NORMAL     = 0x0
	FADV_RANDOM     = 0x1
	FADV_SEQUENTIAL = 0x2
	FADV_WILLNEED   = 0x3
	FADV_DONTNEED   = 0x4
	FADV_NOREUSE    = 0x5
)

type RawSockaddrInet4 struct {
	Len    uint8
	Family uint8
	Port   uint16
	Addr   [4]byte /* in_addr */
	Zero   [8]int8
}

type RawSockaddrInet6 struct {
	Len      uint8
	Family   uint8
	Port     uint16
	Flowinfo uint32
	Addr     [16]byte /* in6_addr */
	Scope_id uint32
}

type RawSockaddrUnix struct {
	Len    uint8
	Family uint8
	Path   [104]int8
}

type RawSockaddrDatalink struct {
	Len    uint8
	Family uint8
	Index  uint16
	Type   uint8
	Nlen   uint8
	Alen   uint8
	Slen   uint8
	Data   [12]int8
}

type RawSockaddr struct {
	Len    uint8
	Family uint8
	Data   [14]int8
}

type RawSockaddrAny struct {
	Addr RawSockaddr
	Pad  [92]int8
}

type _Socklen uint32

type Linger struct {
	Onoff  int32
	Linger int32
}

type Iovec struct {
	Base *byte
	Len  uint32
}

type IPMreq struct {
	Multiaddr [4]byte /* in_addr */
	Interface [4]byte /* in_addr */
}

type IPv6Mreq struct {
	Multiaddr [16]byte /* in6_addr */
	Interface uint32
}

type Msghdr struct {
	Name       *byte
	Namelen    uint32
	Iov        *Iovec
	Iovlen     int32
	Control    *byte
	Controllen uint32
	Flags      int32
}

type Cmsghdr struct {
	Len   uint32
	Level int32
	Type  int32
}

type Inet6Pktinfo struct {
	Addr    [16]byte /* in6_addr */
	Ifindex uint32
}

type IPv6MTUInfo struct {
	Addr RawSockaddrInet6
	Mtu  uint32
}

type ICMPv6Filter struct {
	Filt [8]uint32
}

const (
	SizeofSockaddrInet4    = 0x10
	SizeofSockaddrInet6    = 0x1c
	SizeofSockaddrAny      = 0x6c
	SizeofSockaddrUnix     = 0x6a
	SizeofSockaddrDatalink = 0x14
	SizeofLinger           = 0x8
	SizeofIovec            = 0x8
	SizeofIPMreq           = 0x8
	SizeofIPv6Mreq         = 0x14
	SizeofMsghdr           = 0x1c
	SizeofCmsghdr          = 0xc
	SizeofInet6Pktinfo     = 0x14
	SizeofIPv6MTUInfo      = 0x20
	SizeofICMPv6Filter     = 0x20
)

const (
	PTRACE_TRACEME = 0x0
	PTRACE_CONT    = 0x7
	PTRACE_KILL    = 0x8
)

type Kevent_t struct {
	Ident  uint32
	Filter uint32
	Flags  uint32
	Fflags uint32
	Data   int64
	Udata  int32
}

type FdSet struct {
	Bits [8]uint32
}

const (
	SizeofIfMsghdr         = 0x98
	SizeofIfData           = 0x84
	SizeofIfaMsghdr        = 0x18
	SizeofIfAnnounceMsghdr = 0x18
	SizeofRtMsghdr         = 0x78
	SizeofRtMetrics        = 0x50
)

type IfMsghdr struct {
	Msglen    uint16
	Version   uint8
	Type      uint8
	Addrs     int32
	Flags     int32
	Index     uint16
	Pad_cgo_0 [2]byte
	Data      IfData
	Pad_cgo_1 [4]byte
}

type IfData struct {
	Type       uint8
	Addrlen    uint8
	Hdrlen     uint8
	Pad_cgo_0  [1]byte
	Link_state int32
	Mtu        uint64
	Metric     uint64
	Baudrate   uint64
	Ipackets   uint64
	Ierrors    uint64
	Opackets   uint64
	Oerrors    uint64
	Collisions uint64
	Ibytes     uint64
	Obytes     uint64
	Imcasts    uint64
	Omcasts    uint64
	Iqdrops    uint64
	Noproto    uint64
	Lastchange Timespec
}

type IfaMsghdr struct {
	Msglen    uint16
	Version   uint8
	Type      uint8
	Addrs     int32
	Flags     int32
	Metric    int32
	Index     uint16
	Pad_cgo_0 [6]byte
}

type IfAnnounceMsghdr struct {
	Msglen  uint16
	Version uint8
	Type    uint8
	Index   uint16
	Name    [16]int8
	What    uint16
}

type RtMsghdr struct {
	Msglen    uint16
	Version   uint8
	Type      uint8
	Index     uint16
	Pad_cgo_0 [2]byte
	Flags     int32
	Addrs     int32
	Pid       int32
	Seq       int32
	Errno     int32
	Use       int32
	Inits     int32
	Pad_cgo_1 [4]byte
	Rmx       RtMetrics
}

type RtMetrics struct {
	Locks    uint64
	Mtu      uint64
	Hopcount uint64
	Recvpipe uint64
	Sendpipe uint64
	Ssthresh uint64
	Rtt      uint64
	Rttvar   uint64
	Expire   int64
	Pksent   int64
}

type Mclpool [0]byte

const (
	SizeofBpfVersion = 0x4
	SizeofBpfStat    = 0x80
	SizeofBpfProgram = 0x8
	SizeofBpfInsn    = 0x8
	SizeofBpfHdr     = 0x14
)

type BpfVersion struct {
	Major uint16
	Minor uint16
}

type BpfStat struct {
	Recv    uint64
	Drop    uint64
	Capt    uint64
	Padding [13]uint64
}

type BpfProgram struct {
	Len   uint32
	Insns *BpfInsn
}

type BpfInsn struct {
	Code uint16
	Jt   uint8
	Jf   uint8
	K    uint32
}

type BpfHdr struct {
	Tstamp    BpfTimeval
	Caplen    uint32
	Datalen   uint32
	Hdrlen    uint16
	Pad_cgo_0 [2]byte
}

type BpfTimeval struct {
	Sec  int32
	Usec int32
}

type Termios struct {
	Iflag  uint32
	Oflag  uint32
	Cflag  uint32
	Lflag  uint32
	Cc     [20]uint8
	Ispeed int32
	Ospeed int32
}

type Winsize struct {
	Row    uint16
	Col    uint16
	Xpixel uint16
	Ypixel uint16
}

type Ptmget struct {
	Cfd int32
	Sfd int32
	Cn  [1024]byte
	Sn  [1024]byte
}

const (
	AT_FDCWD            = -0x64
	AT_EACCESS          = 0x100
	AT_SYMLINK_NOFOLLOW = 0x200
	AT_SYMLINK_FOLLOW   = 0x400
	AT_REMOVEDIR        = 0x800
)

type PollFd struct {
	Fd      int32
	Events  int16
	Revents int16
}

const (
	POLLERR    = 0x8
	POLLHUP    = 0x10
	POLLIN     = 0x1
	POLLNVAL   = 0x20
	POLLOUT    = 0x4
	POLLPRI    = 0x2
	POLLRDBAND = 0x80
	POLLRDNORM = 0x40
	POLLWRBAND = 0x100
	POLLWRNORM = 0x4
)

type Sysctlnode struct {
	Flags           uint32
	Num             int32
	Name            [32]int8
	Ver             uint32
	X__rsvd         uint32
	Un              [16]byte
	X_sysctl_size   [8]byte
	X_sysctl_func   [8]byte
	X_sysctl_parent [8]byte
	X_sysctl_desc   [8]byte
}

type Utsname struct {
	Sysname  [256]byte
	Nodename [256]byte
	Release  [256]byte
	Version  [256]byte
	Machine  [256]byte
}

const SizeofUvmexp = 0x278

type Uvmexp struct {
	Pagesize           int64
	Pagemask           int64
	Pageshift          int64
	Npages             int64
	Free               int64
	Active             int64
	Inactive           int64
	Paging             int64
	Wired              int64
	Zeropages          int64
	Reserve_pagedaemon int64
	Reserve_kernel     int64
	Freemin            int64
	Freetarg           int64
	Inactarg           int64
	Wiredmax           int64
	Nswapdev           int64
	Swpages            int64
	Swpginuse          int64
	Swpgonly           int64
	Nswget             int64
	Unused1            int64
	Cpuhit             int64
	Cpumiss            int64
	Faults             int64
	Traps              int64
	Intrs              int64
	Swtch              int64
	Softs              int64
	Syscalls           int64
	Pageins            int64
	Swapins            int64
	Swapouts           int64
	Pgswapin           int64
	Pgswapout          int64
	Forks              int64
	Forks_ppwait       int64
	Forks_sharevm      int64
	Pga_zerohit        int64
	Pga_zeromiss       int64
	Zeroaborts         int64
	Fltnoram           int64
	Fltnoanon          int64
	Fltpgwait          int64
	Fltpgrele          int64
	Fltrelck           int64
	Fltrelckok         int64
	Fltanget           int64
	Fltanretry         int64
	Fltamcopy          int64
	Fltnamap           int64
	Fltnomap           int64
	Fltlget            int64
	Fltget             int64
	Flt_anon           int64
	Flt_acow           int64
	Flt_obj            int64
	Flt_prcopy         int64
	Flt_przero         int64
	Pdwoke             int64
	Pdrevs             int64
	Unused4            int64
	Pdfreed            int64
	Pdscans            int64
	Pdanscan           int64
	Pdobscan           int64
	Pdreact            int64
	Pdbusy             int64
	Pdpageouts         int64
	Pdpending          int64
	Pddeact            int64
	Anonpages          int64
	Filepages          int64
	Execpages          int64
	Colorhit           int64
	Colormiss          int64
	Ncolors            int64
	Bootpages          int64
	Poolpages          int64
}

const SizeofClockinfo = 0x14

type Clockinfo struct {
	Hz      int32
	Tick    int32
	Tickadj int32
	Stathz  int32
	Profhz  int32
}
